{"name": "hauling-qr-trip-system", "version": "1.0.0", "description": "Hauling QR Trip Management System", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "test": "jest", "test:mobile": "jest --config tests/driver-qr-system/mobile/jest.config.js", "test:mobile:watch": "jest --config tests/driver-qr-system/mobile/jest.config.js --watch", "test:mobile:coverage": "jest --config tests/driver-qr-system/mobile/jest.config.js --coverage", "test:browser-compatibility": "node run-browser-compatibility-tests.js", "db:migrate": "node database/run-migration.js"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "pg": "^8.11.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.3"}, "devDependencies": {"chromedriver": "^119.0.1", "geckodriver": "^4.2.1", "jest": "^29.5.0", "nodemon": "^2.0.22", "selenium-webdriver": "^4.34.0", "supertest": "^6.3.3"}, "keywords": ["hauling", "qr", "trip", "management", "logistics"], "author": "Hauling QR System Team", "license": "MIT"}