---
type: "always_apply"
---

Please use sequential thinking to review and analyze the modified code files. Follow these steps:

1. **Review**: Examine the changes made to understand what was modified, added, or removed
2. **Understand**: Analyze the purpose and context of the changes within the broader codebase
3. **Identify**: Look for any missing code patterns, incomplete logic, potential bugs, or inconsistencies

Focus on:
- Missing error handling or edge cases
- Incomplete implementation patterns
- Logic gaps or inconsistencies
- Missing type definitions or validations
- Incomplete test coverage areas
- Missing documentation or comments for complex logic
- Potential security vulnerabilities
- Performance optimization opportunities

Provide specific, actionable feedback on what needs to be addressed.
