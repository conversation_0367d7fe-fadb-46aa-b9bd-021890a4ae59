#!/bin/bash

# CRITICAL CORS Persistence Test for Hauling QR Trip System
# Tests deployment → reboot → login cycle to ensure CORS works after reboot

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"
API_URL="https://api.${PRODUCTION_DOMAIN}"
FRONTEND_URL="https://${PRODUCTION_DOMAIN}"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "🚨 CRITICAL CORS PERSISTENCE TEST"
echo "🌐 Domain: $PRODUCTION_DOMAIN"
echo "📅 $(date)"
echo

# Test 1: Check PM2 Environment Variables
test_pm2_environment() {
    log_info "Test 1: Checking PM2 environment variables..."
    
    local pm2_env_output
    if [[ "$EUID" -eq 0 ]]; then
        pm2_env_output=$(sudo -u ubuntu pm2 show hauling-qr-server 2>/dev/null || echo "")
    else
        pm2_env_output=$(pm2 show hauling-qr-server 2>/dev/null || echo "")
    fi
    
    if echo "$pm2_env_output" | grep -q "NGINX_PROXY_MODE.*true"; then
        log_success "✅ NGINX_PROXY_MODE=true found in PM2 environment"
        return 0
    else
        log_error "❌ NGINX_PROXY_MODE not found in PM2 environment"
        echo "PM2 environment variables:"
        echo "$pm2_env_output" | grep -A 20 "env:" || true
        return 1
    fi
}

# Test 2: Check NGINX CORS Configuration
test_nginx_cors() {
    log_info "Test 2: Checking NGINX CORS configuration..."
    
    if grep -q "Access-Control-Allow-Origin.*https://${PRODUCTION_DOMAIN}" /etc/nginx/sites-available/hauling-qr-system; then
        log_success "✅ NGINX CORS headers configured for $PRODUCTION_DOMAIN"
        return 0
    else
        log_error "❌ NGINX CORS headers not properly configured"
        echo "Current CORS configuration:"
        grep -n "Access-Control-Allow-Origin" /etc/nginx/sites-available/hauling-qr-system || true
        return 1
    fi
}

# Test 3: Test CORS Headers in HTTP Response
test_cors_headers() {
    log_info "Test 3: Testing CORS headers in HTTP response..."
    
    local cors_response
    cors_response=$(curl -s -I -H "Origin: $FRONTEND_URL" "$API_URL/api/health" 2>/dev/null || echo "")
    
    if echo "$cors_response" | grep -qi "access-control-allow-origin.*$PRODUCTION_DOMAIN"; then
        log_success "✅ CORS headers present in HTTP response"
        return 0
    else
        log_error "❌ CORS headers missing in HTTP response"
        echo "Response headers:"
        echo "$cors_response"
        return 1
    fi
}

# Test 4: Test Login API Endpoint
test_login_endpoint() {
    log_info "Test 4: Testing login API endpoint..."
    
    local login_response
    login_response=$(curl -s -w "%{http_code}" -H "Origin: $FRONTEND_URL" -H "Content-Type: application/json" -X POST "$API_URL/api/auth/login" -d '{"username":"test","password":"test"}' 2>/dev/null || echo "000")
    
    local http_code="${login_response: -3}"
    
    if [[ "$http_code" == "401" ]] || [[ "$http_code" == "400" ]]; then
        log_success "✅ Login endpoint responding (HTTP $http_code - expected for invalid credentials)"
        return 0
    elif [[ "$http_code" == "502" ]]; then
        log_error "❌ Login endpoint returning 502 Bad Gateway"
        return 1
    else
        log_warning "⚠️ Login endpoint returned HTTP $http_code"
        return 1
    fi
}

# Test 5: Check Service Status
test_service_status() {
    log_info "Test 5: Checking service status..."
    
    local failed=0
    
    if systemctl is-active --quiet nginx; then
        log_success "✅ NGINX is running"
    else
        log_error "❌ NGINX is not running"
        ((failed++))
    fi
    
    if systemctl is-active --quiet postgresql; then
        log_success "✅ PostgreSQL is running"
    else
        log_error "❌ PostgreSQL is not running"
        ((failed++))
    fi
    
    local pm2_status
    if [[ "$EUID" -eq 0 ]]; then
        pm2_status=$(sudo -u ubuntu pm2 list 2>/dev/null | grep "hauling-qr-server" || echo "")
    else
        pm2_status=$(pm2 list 2>/dev/null | grep "hauling-qr-server" || echo "")
    fi
    
    if echo "$pm2_status" | grep -q "online"; then
        log_success "✅ PM2 hauling-qr-server is online"
    else
        log_error "❌ PM2 hauling-qr-server is not online"
        echo "PM2 status: $pm2_status"
        ((failed++))
    fi
    
    return $failed
}

# Test 6: Ecosystem Config Validation
test_ecosystem_config() {
    log_info "Test 6: Validating ecosystem.config.js..."
    
    if [[ -f "/var/www/hauling-qr-system/ecosystem.config.js" ]]; then
        if grep -q "NGINX_PROXY_MODE: 'true'" /var/www/hauling-qr-system/ecosystem.config.js; then
            log_success "✅ ecosystem.config.js has NGINX_PROXY_MODE=true"
            
            if grep -q "PRODUCTION_DOMAIN: '${PRODUCTION_DOMAIN}'" /var/www/hauling-qr-system/ecosystem.config.js; then
                log_success "✅ ecosystem.config.js has correct domain: $PRODUCTION_DOMAIN"
                return 0
            else
                log_error "❌ ecosystem.config.js has incorrect domain"
                grep -n "PRODUCTION_DOMAIN" /var/www/hauling-qr-system/ecosystem.config.js || true
                return 1
            fi
        else
            log_error "❌ ecosystem.config.js missing NGINX_PROXY_MODE=true"
            return 1
        fi
    else
        log_error "❌ ecosystem.config.js not found"
        return 1
    fi
}

# Main test execution
main() {
    local total_tests=6
    local passed_tests=0
    
    echo "🧪 Running $total_tests critical CORS persistence tests..."
    echo
    
    # Run all tests
    test_service_status && ((passed_tests++)) || true
    test_ecosystem_config && ((passed_tests++)) || true
    test_pm2_environment && ((passed_tests++)) || true
    test_nginx_cors && ((passed_tests++)) || true
    test_cors_headers && ((passed_tests++)) || true
    test_login_endpoint && ((passed_tests++)) || true
    
    echo
    echo "🎯 TEST RESULTS:"
    echo "Passed: $passed_tests/$total_tests"
    
    if [[ $passed_tests -eq $total_tests ]]; then
        log_success "🎉 ALL TESTS PASSED - CORS should work after reboot!"
        echo "✅ Login functionality should work at: $FRONTEND_URL"
        return 0
    else
        log_error "❌ SOME TESTS FAILED - CORS may fail after reboot"
        echo "❌ Login functionality may not work after system restart"
        return 1
    fi
}

# Run the tests
main "$@"
