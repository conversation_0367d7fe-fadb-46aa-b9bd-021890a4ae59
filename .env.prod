# =============================================================================
# HAULING QR TRIP SYSTEM - PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains production-specific environment variables
# Created for dual environment system with Cloudflare compatibility
# Supports automatic development/production mode switching with automated IP detection

# =============================================================================
# ENVIRONMENT MODE CONFIGURATION
# =============================================================================
# Set to 'development' or 'production'
NODE_ENV=production

# Enable HTTPS (true/false) - DISABLED for production (Cloudflare handles SSL termination)
ENABLE_HTTPS=false

# Automatic IP detection (true/false) - when true, overrides hardcoded IPs
AUTO_DETECT_IP=true

# Manual IP override (only used when AUTO_DETECT_IP=false)
MANUAL_IP=

# =============================================================================
# DATABASE CONFIGURATION
# Using consistent postgres/PostgreSQLPassword credentials for both environments
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=PostgreSQLPassword123

# Database pool configuration
DB_POOL_MAX=25
DB_POOL_MIN=5

# =============================================================================
# CENTRALIZED PORT CONFIGURATION - CLOUDFLARE COMPATIBLE
# =============================================================================
# All port configurations are centralized here for easy modification
# All other files will read these values from this .env file

# Frontend Port (React app uses this)
FRONTEND_PORT=3000
PORT=3000
CLIENT_PORT=3000

# Backend HTTP Port (CLOUDFLARE COMPATIBLE - port 8080)
BACKEND_HTTP_PORT=8080
BACKEND_PORT=8080
SERVER_PORT=8080

# Backend HTTPS Port (for production with camera access - Cloudflare compatible)
HTTPS_PORT=8443

# Port Usage Summary:
# - Frontend: ALWAYS port 3000 (HTTP/HTTPS)
# - Backend HTTP: port 8080 (Cloudflare compatible)
# - Backend HTTPS: port 8443 (Cloudflare compatible, required for camera access)

# =============================================================================
# PRODUCTION URLS - CLOUDFLARE INTEGRATION
# =============================================================================
# Production domain settings - DYNAMIC CONFIGURATION
# These values can be overridden by environment variables during deployment
# Example: PRODUCTION_DOMAIN=truckhaul.local for WSL testing
# Example: PRODUCTION_DOMAIN=truckhaul.top for production
# NOTE: These are fallback values - deployment scripts will override with environment variables
PRODUCTION_DOMAIN=truckhaul.top
API_BASE_URL=https://api.truckhaul.top
FRONTEND_URL=https://truckhaul.top
CLIENT_URL=https://truckhaul.top

# =============================================================================
# SIMPLIFIED CORS CONFIGURATION
# =============================================================================
# Allowed origins (simplified for security)
ALLOWED_ORIGINS=localhost,127.0.0.1,0.0.0.0,truckhaul.top,api.truckhaul.top

# JWT Authentication
JWT_SECRET=hauling_qr_jwt_secret_2025_secure_key_for_production
JWT_EXPIRY=24h

# =============================================================================
# SSL/HTTPS CONFIGURATION - DISABLED FOR CLOUDFLARE
# =============================================================================
# Development SSL certificates (self-signed)
SSL_CERT_PATH_DEV=./ssl/dev/server.crt
SSL_KEY_PATH_DEV=./ssl/dev/server.key
SSL_CA_PATH_DEV=

# Production SSL certificates (CA-signed) - NOT USED WITH CLOUDFLARE
SSL_CERT_PATH_PROD=./ssl/production/fullchain.crt
SSL_KEY_PATH_PROD=./ssl/production/server.key
SSL_CA_PATH_PROD=./ssl/production/intermediate.crt

# =============================================================================
# CLIENT CONFIGURATION
# =============================================================================
# React development server port
CLIENT_PORT=3000

# Enable React HTTPS development server - DISABLED for production
CLIENT_HTTPS=false
# Client API URL (auto-generated if AUTO_DETECT_IP=true)
# REACT_APP_API_URL=https://api.truckhaul.top

# Client WebSocket URL (auto-generated if AUTO_DETECT_IP=true)
# REACT_APP_WS_URL=wss://api.truckhaul.top

# Development features - DISABLED for production
REACT_APP_ENABLE_DEV_TOOLS=false
REACT_APP_DEBUG_MODE=false
REACT_APP_DEV_TUNNEL=false
GENERATE_SOURCEMAP=false

# Webpack Dev Server Configuration for Dev Tunnels - DISABLED for production
FAST_REFRESH=false
WDS_SOCKET_HOST=localhost
WDS_SOCKET_PORT=0
DISABLE_NEW_JSX_TRANSFORM=true

# =============================================================================
# QR CODE CONFIGURATION
# =============================================================================
QR_CODE_SIZE=200
QR_CODE_QUALITY=H

# QR Scanner settings
REACT_APP_QR_CAMERA_FACING=environment
REACT_APP_QR_SCAN_DELAY=500

# QR Code Validation Settings
# Set to 'true' to enable strict checksum validation (not recommended for production)
STRICT_QR_VALIDATION=false

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# =============================================================================
# RATE LIMITING CONFIGURATION - PRODUCTION SETTINGS
# =============================================================================
# General rate limiting (15 minutes window, 20,000 requests - production flexible)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=20000

# Authentication rate limiting (15 minutes window, 1000 attempts - production security)
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=1000

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# These settings are automatically applied when NODE_ENV=production

# Production JWT secret (override for production)
JWT_SECRET_PROD=hauling_qr_jwt_secret_2025_secure_key_for_production

# Production rate limits (flexible for production)
RATE_LIMIT_MAX_REQUESTS_PROD=20000

# Production database settings
DB_HOST_PROD=localhost
DB_NAME_PROD=hauling_qr_system
DB_USER_PROD=postgres
DB_PASSWORD_PROD=PostgreSQLPassword123

# Production domain settings (when not using auto-detection)
PRODUCTION_DOMAIN=truckhaul.top

# =============================================================================
# DEVELOPMENT OVERRIDES - DISABLED FOR PRODUCTION
# =============================================================================
# These settings are automatically applied when NODE_ENV=development

# Development features - DISABLED
DEV_ENABLE_CORS_ALL=false
DEV_ENABLE_DETAILED_LOGS=false
DEV_DISABLE_RATE_LIMITING=false

# Production rate limiting control - ENABLED for production security
PROD_DISABLE_RATE_LIMITING=false

# =============================================================================
# OPTIONAL SERVICES
# =============================================================================
# Redis configuration (optional)
REDIS_URL=redis://localhost:6379

# Email configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Monitoring and Logging Configuration
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=warn

# Enhanced Logging Configuration - PRODUCTION SETTINGS
# Log levels: error, warn, info, debug
LOG_LEVEL_PRODUCTION=warn
LOG_LEVEL_DEVELOPMENT=info

# Monitoring service intervals (in milliseconds)
STATUS_SYNC_MONITOR_INTERVAL=300000
SHIFT_SYNC_MONITOR_INTERVAL=180000
ENHANCED_SHIFT_STATUS_INTERVAL=300000

# Monitoring logging control - PRODUCTION SETTINGS
MONITORING_LOGS_ENABLED=true
MONITORING_LOG_LEVEL=warn
MONITORING_DEDUPLICATION_ENABLED=true
MONITORING_DEDUPLICATION_WINDOW_MS=300000
MONITORING_MAX_DUPLICATES=2

# Performance logging - PRODUCTION SETTINGS
PERFORMANCE_LOGGING_ENABLED=true
SLOW_QUERY_THRESHOLD_MS=1000
LOG_ALL_REQUESTS=false

# Console Output Control (suppress verbose messages) - PRODUCTION SETTINGS
SUPPRESS_DATABASE_QUERY_LOGS=true
SUPPRESS_SYNC_SUCCESS_MESSAGES=true
SUPPRESS_CLIENT_AUTH_MESSAGES=true
SUPPRESS_CONNECTION_MESSAGES=true
SUPPRESS_CORS_CONFIG_MESSAGES=true
SUPPRESS_SHIFT_QUERY_DEBUG=true
SUPPRESS_SHIFT_COMPLETION_MESSAGES=true
SUPPRESS_DRIVER_SYNC_LOGS=true

# =============================================================================
# AUTOMATIC CONFIGURATION NOTES
# =============================================================================
# The following variables are automatically set by the startup scripts:
# - REACT_APP_API_URL: Auto-generated based on NODE_ENV, ENABLE_HTTPS, and detected IP
# - REACT_APP_WS_URL: Auto-generated based on NODE_ENV, ENABLE_HTTPS, and detected IP
# - FRONTEND_URL: Auto-generated for CORS configuration
# - SSL_CERT_PATH: Auto-selected based on NODE_ENV
# - SSL_KEY_PATH: Auto-selected based on NODE_ENV
# - SSL_CA_PATH: Auto-selected based on NODE_ENV

# Auto-generated URLs (will be updated by startup script)
# Production URLs will be set by environment loader
REACT_APP_USE_HTTPS=false
REACT_APP_LOCAL_NETWORK_IP=
REACT_APP_API_URL=https://api.truckhaul.top/api
REACT_APP_WS_URL=wss://api.truckhaul.top/ws
