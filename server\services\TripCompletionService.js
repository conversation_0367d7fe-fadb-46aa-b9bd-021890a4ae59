/**
 * Trip Completion Service
 * 
 * Provides unified trip completion logic that can be used by:
 * 1. QR Scanner (existing)
 * 2. Admin Dashboard (new)
 * 3. Driver Connect PWA (new)
 * 
 * Ensures consistent trip completion behavior across all methods.
 */



class TripCompletionService {
  /**
   * Complete a trip with consistent logic across all completion methods
   * @param {Object} client - Database client (transaction)
   * @param {Object} trip - Trip data from trip_logs
   * @param {Object} options - Completion options
   * @param {string} options.completion_method - Method used: 'qr_scanner', 'admin_dashboard', 'driver_pwa'
   * @param {Object} options.location - Location data (optional, for QR scanner)
   * @param {Object} options.user - User performing completion
   * @param {string} options.completion_reason - Reason for completion (optional)
   * @param {Date} options.completion_time - Custom completion time (defaults to now)
   * @returns {Object} Completed trip data
   */
  async completeTrip(client, trip, options = {}) {
    const {
      completion_method = 'manual',
      location = null,
      user = null,
      completion_reason = null,
      completion_time = new Date()
    } = options;

    // Validate trip can be completed
    const validation = this.validateTripCompletion(trip);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    const now = completion_time;

    // Calculate durations
    const durations = this.calculateTripDurations(trip, now);

    // Prepare completion metadata
    const completionMetadata = this.buildCompletionMetadata(
      trip,
      completion_method,
      location,
      user,
      completion_reason,
      durations,
      now
    );

    // Update trip status to completed
    const completedTrip = await client.query(`
      UPDATE trip_logs
      SET status = $1,
          trip_completed_time = $2,
          total_duration_minutes = $3,
          travel_duration_minutes = $4,
          updated_at = $2,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $5::jsonb
      WHERE id = $6
      RETURNING *
    `, [
      'trip_completed',
      now,
      durations.total,
      durations.forward_travel,
      JSON.stringify(completionMetadata),
      trip.id
    ]);

    if (completedTrip.rows.length === 0) {
      throw new Error('Failed to update trip status to completed');
    }

    const updatedTrip = completedTrip.rows[0];

    return {
      success: true,
      trip: updatedTrip,
      completion_method,
      durations,
      metadata: completionMetadata
    };
  }

  /**
   * Validate that a trip can be completed
   * @param {Object} trip - Trip data
   * @returns {Object} Validation result
   */
  validateTripCompletion(trip) {
    // Check if trip exists
    if (!trip || !trip.id) {
      return {
        valid: false,
        error: 'Trip data is required'
      };
    }

    // Check trip status - only unloading_end trips can be completed
    if (trip.status !== 'unloading_end') {
      return {
        valid: false,
        error: `Cannot complete trip with status '${trip.status}'. Trip must be in 'unloading_end' status.`,
        current_status: trip.status,
        required_status: 'unloading_end'
      };
    }

    // Check if trip is already completed
    if (trip.trip_completed_time) {
      return {
        valid: false,
        error: 'Trip is already completed',
        completed_at: trip.trip_completed_time
      };
    }

    // Validate required timestamps exist
    if (!trip.loading_start_time) {
      return {
        valid: false,
        error: 'Trip missing loading_start_time - cannot calculate durations'
      };
    }

    if (!trip.unloading_end_time) {
      return {
        valid: false,
        error: 'Trip missing unloading_end_time - cannot complete from current status'
      };
    }

    return {
      valid: true,
      message: 'Trip can be completed'
    };
  }

  /**
   * Calculate all trip durations
   * @param {Object} trip - Trip data
   * @param {Date} completionTime - Time of completion
   * @returns {Object} Duration calculations
   */
  calculateTripDurations(trip, completionTime) {
    const durations = {};

    // Total trip duration (loading_start to completion)
    if (trip.loading_start_time) {
      durations.total = Math.round(
        (completionTime - new Date(trip.loading_start_time)) / (1000 * 60)
      );
    }

    // Forward travel duration (loading_end to unloading_start) if not already set
    durations.forward_travel = trip.travel_duration_minutes;
    if (!durations.forward_travel && trip.loading_end_time && trip.unloading_start_time) {
      durations.forward_travel = Math.round(
        (new Date(trip.unloading_start_time) - new Date(trip.loading_end_time)) / (1000 * 60)
      );
    }

    // Return travel duration (unloading_end to completion)
    if (trip.unloading_end_time) {
      durations.return_travel = Math.round(
        (completionTime - new Date(trip.unloading_end_time)) / (1000 * 60)
      );
    }

    // Individual phase durations (if not already calculated)
    if (trip.loading_start_time && trip.loading_end_time && !trip.loading_duration_minutes) {
      durations.loading = Math.round(
        (new Date(trip.loading_end_time) - new Date(trip.loading_start_time)) / (1000 * 60)
      );
    }

    if (trip.unloading_start_time && trip.unloading_end_time && !trip.unloading_duration_minutes) {
      durations.unloading = Math.round(
        (new Date(trip.unloading_end_time) - new Date(trip.unloading_start_time)) / (1000 * 60)
      );
    }

    return durations;
  }

  /**
   * Build completion metadata for notes field
   * @param {Object} trip - Trip data
   * @param {string} completion_method - Method used for completion
   * @param {Object} location - Location data (optional)
   * @param {Object} user - User data (optional)
   * @param {string} completion_reason - Reason for completion (optional)
   * @param {Object} durations - Calculated durations
   * @param {Date} completionTime - Time of completion
   * @returns {Object} Metadata object
   */
  buildCompletionMetadata(trip, completion_method, location, user, completion_reason, durations, completionTime) {
    const metadata = {
      completion_method,
      completion_timestamp: completionTime.toISOString(),
      workflow_type: 'enhanced_trip_completion'
    };

    // Add duration information
    if (durations.return_travel !== undefined) {
      metadata.return_travel_duration_minutes = durations.return_travel;
      metadata.return_travel_start = trip.unloading_end_time;
      metadata.return_travel_end = completionTime.toISOString();
    }

    // Add user information
    if (user) {
      metadata.completed_by_user_id = user.id;
      metadata.completed_by_user_name = user.full_name || user.username;
    }

    // Add location information (for QR scanner method)
    if (location) {
      metadata.completion_location_id = location.id;
      metadata.completion_location_name = location.name;
      metadata.completion_location_type = location.type;
    }

    // Add completion reason
    if (completion_reason) {
      metadata.completion_reason = completion_reason;
    }

    // Add method-specific metadata
    switch (completion_method) {
      case 'qr_scanner':
        metadata.completion_source = 'qr_code_scan';
        metadata.workflow_completed = 'loading_start → loading_end → unloading_start → unloading_end → qr_completion';
        break;
      case 'admin_dashboard':
        metadata.completion_source = 'admin_manual';
        metadata.workflow_completed = 'loading_start → loading_end → unloading_start → unloading_end → admin_completion';
        break;
      case 'driver_pwa':
        metadata.completion_source = 'driver_manual';
        metadata.workflow_completed = 'loading_start → loading_end → unloading_start → unloading_end → driver_completion';
        break;
      default:
        metadata.completion_source = 'manual';
        metadata.workflow_completed = 'loading_start → loading_end → unloading_start → unloading_end → manual_completion';
    }

    return metadata;
  }

  /**
   * Get trips that can be completed for a specific truck
   * @param {Object} client - Database client
   * @param {number} truckId - Truck ID
   * @returns {Array} Completable trips
   */
  async getCompletableTrips(client, truckId) {
    const query = `
      SELECT
        tl.*,
        a.assignment_code,
        a.truck_id,
        dt.truck_number,
        ll.name as loading_location_name,
        ul.name as unloading_location_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND tl.status = 'unloading_end'
        AND tl.trip_completed_time IS NULL
      ORDER BY tl.created_at DESC
    `;

    const result = await client.query(query, [truckId]);
    return result.rows;
  }

  /**
   * Get active trip for a driver (for Driver Connect PWA)
   * @param {Object} client - Database client
   * @param {string} employeeId - Driver employee ID
   * @returns {Object|null} Active trip or null
   */
  async getDriverActiveTrip(client, employeeId) {
    const query = `
      SELECT
        tl.*,
        a.assignment_code,
        a.truck_id,
        dt.truck_number,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        d.full_name as driver_name
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE d.employee_id = $1
        AND tl.status IN ('assigned','loading_start','loading_end','unloading_start','unloading_end')
        AND tl.status != 'trip_completed'
        AND tl.status != 'stopped'
      ORDER BY tl.created_at DESC
      LIMIT 1
    `;

    const result = await client.query(query, [employeeId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  /**
   * Stop a trip (mark as stopped without completion)
   * @param {Object} client - Database client
   * @param {Object} trip - Trip object to stop
   * @param {Object} options - Stop options
   * @returns {Object} Stop result
   */
  async stopTrip(client, trip, options = {}) {
    const validation = this.validateTripForStopping(trip);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    const {
      stop_method = 'manual',
      user = null,
      stop_reason = null,
      stop_time = new Date()
    } = options;

    // Calculate durations up to the stop point
    const durations = this.calculateTripDurations(trip, stop_time);

    // Build stop metadata
    const stopMetadata = this.buildStopMetadata(stop_method, user, stop_reason, durations);

    // Update trip status to 'stopped'
    const updateQuery = `
      UPDATE trip_logs
      SET
        status = 'stopped',
        stopped_reported_at = $2,
        stopped_reason = $5,
        total_duration_minutes = $3,
        notes = COALESCE(notes, '{}'::jsonb) || $4::jsonb,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `;

    const result = await client.query(updateQuery, [
      trip.id,
      stop_time,
      durations.total,
      JSON.stringify(stopMetadata),
      stop_reason || 'Trip stopped via DriverConnect PWA'
    ]);

    return {
      success: true,
      trip: result.rows[0],
      stop_method,
      durations,
      metadata: stopMetadata
    };
  }

  /**
   * Validate if a trip can be stopped
   * @param {Object} trip - Trip object
   * @returns {Object} Validation result
   */
  validateTripForStopping(trip) {
    if (!trip) {
      return { valid: false, error: 'Trip not found' };
    }

    if (trip.status === 'trip_completed') {
      return { valid: false, error: 'Trip is already completed' };
    }

    if (trip.status === 'stopped') {
      return { valid: false, error: 'Trip is already stopped' };
    }

    if (trip.trip_stopped_time) {
      return { valid: false, error: 'Trip already has a stop time' };
    }

    // Allow stopping from any active status (assigned, loading_start, loading_end, unloading_start, unloading_end)
    const stoppableStatuses = ['assigned', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end'];
    if (!stoppableStatuses.includes(trip.status)) {
      return { valid: false, error: `Trip cannot be stopped from status: ${trip.status}` };
    }

    return { valid: true };
  }

  /**
   * Build stop metadata
   * @param {string} stopMethod - Stop method
   * @param {Object} user - User who stopped the trip
   * @param {string} stopReason - Reason for stopping
   * @param {Object} durations - Duration calculations
   * @returns {Object} Stop metadata
   */
  buildStopMetadata(stopMethod, user, stopReason, durations) {
    const metadata = {
      stop_method: stopMethod,
      stop_timestamp: new Date().toISOString(),
      workflow_type: 'trip_stopping',
      stop_reason: stopReason || 'No reason provided'
    };

    // Add duration information
    if (durations.total) {
      metadata.total_duration_minutes = durations.total;
    }

    // Add user information
    if (user) {
      metadata.stopped_by_user_id = user.id;
      metadata.stopped_by_user_name = user.full_name || user.name;
    }

    // Add method-specific metadata
    switch (stopMethod) {
      case 'admin_dashboard':
        metadata.stop_source = 'admin_manual';
        metadata.workflow_stopped = 'admin_stop';
        break;
      case 'driver_pwa':
        metadata.stop_source = 'driver_manual';
        metadata.workflow_stopped = 'driver_stop';
        break;
      case 'qr_scanner':
        metadata.stop_source = 'qr_code_scan';
        metadata.workflow_stopped = 'qr_stop';
        break;
      default:
        metadata.stop_source = 'manual';
        metadata.workflow_stopped = 'manual_stop';
    }

    return metadata;
  }
}

module.exports = new TripCompletionService();
