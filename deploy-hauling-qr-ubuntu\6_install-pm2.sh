#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - MODULAR PM2 INSTALLATION SCRIPT
# =============================================================================
# Version: 1.0.0 - Extracted from auto-deploy.sh
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Description: Self-contained PM2 installation, ecosystem configuration, and process management
# =============================================================================

set -euo pipefail

# Source shared configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/shared-config.sh" || {
  echo "ERROR: shared-config.sh not found"
  exit 1
}

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly SCRIPT_NAME="install-pm2.sh"
readonly LOG_FILE="${LOG_DIR}/pm2-install-$(date +%Y%m%d-%H%M%S).log"

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory with proper permissions
sudo mkdir -p "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  echo -e "${BLUE}[$(ts)] INFO  | $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
  echo -e "${GREEN}[$(ts)] OK    | $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
  echo -e "${YELLOW}[$(ts)] WARN  | $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
  echo -e "${RED}[$(ts)] ERROR | $1${NC}" | tee -a "$LOG_FILE"
}

log_debug() {
  echo -e "${CYAN}[$(ts)] DEBUG | $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# ERROR HANDLING
# =============================================================================
cleanup_on_error() {
  local exit_code=$?
  if [[ $exit_code -ne 0 ]]; then
    log_error "PM2 installation failed with exit code $exit_code"
    log_error "Check log file: $LOG_FILE"
  fi
  exit $exit_code
}

trap cleanup_on_error ERR

# =============================================================================
# INTELLIGENT PM2 INSTALLATION FUNCTIONS
# =============================================================================

# Intelligent PM2 installation with process preservation
install_pm2_intelligent() {
  log_info "🧠 Analyzing PM2 installation with intelligent detection..."

  # Check if PM2 is already installed and healthy
  if command_exists pm2 && pm2_app_is_healthy "hauling-qr-server" "production"; then
    log_success "✅ PM2 is installed and Hauling QR application is healthy - SKIPPING"
    create_phase_completion_marker "pm2-installation" "app-healthy-$(date +%s)"
    return 0
  fi

  # Check if PM2 is installed but needs configuration
  if command_exists pm2; then
    local pm2_version=$(pm2 -v 2>/dev/null || echo "unknown")
    log_info "🔄 PM2 detected: version $pm2_version"

    # Test PM2 functionality
    if pm2 ping >/dev/null 2>&1; then
      log_info "✅ PM2 is functional - checking application status"

      # Check if our application is running
      if pm2 list | grep -q "hauling-qr-server"; then
        local app_status=$(pm2 list | grep "hauling-qr-server" | awk '{print $10}' || echo "unknown")
        log_info "📊 Application status: $app_status"

        if [[ "$app_status" == "online" ]]; then
          # Validate environment variables
          if pm2 env 0 | grep -q "NGINX_PROXY_MODE.*true"; then
            log_success "✅ PM2 and application are properly configured - SKIPPING installation"
            create_phase_completion_marker "pm2-installation" "app-configured-$(date +%s)"
            return 0
          else
            log_warning "⚠️ PM2 application needs environment variable updates"
          fi
        else
          log_warning "⚠️ PM2 application is not online - needs reconfiguration"
        fi
      else
        log_info "🔄 PM2 is installed but Hauling QR application not found - needs configuration"
      fi
    else
      log_warning "⚠️ PM2 is installed but not functional - needs fixing"
    fi
  else
    log_info "🔄 PM2 not found - INSTALLING"
  fi

  # Proceed with installation/configuration using existing function
  if install_pm2; then
    create_phase_completion_marker "pm2-installation" "fresh-install-$(date +%s)"
    return 0
  else
    return 1
  fi
}

# =============================================================================
# LEGACY PM2 INSTALLATION FUNCTIONS
# =============================================================================
install_pm2() {
  log_info "📦 Installing PM2 process manager..."

  # Check if PM2 is already installed with enhanced health check
  if command -v pm2 >/dev/null 2>&1; then
    log_info "PM2 already installed: $(pm2 -v)"

    # Enhanced PM2 health check and fixes from backup script
    log_info "Performing comprehensive PM2 health check..."

    # Fix PM2 permissions first
    if [[ -d "/home/<USER>/.pm2" ]]; then
      log_info "Fixing PM2 permissions for ubuntu user..."
      sudo chown -R ubuntu:ubuntu /home/<USER>/.pm2/ 2>/dev/null || true
      chmod -R 755 /home/<USER>/.pm2/ 2>/dev/null || true
    fi

    local pm2_status_output
    pm2_status_output=$(pm2 status 2>&1 || echo "")

    # Check for permission issues
    if echo "$pm2_status_output" | grep -q "Permission denied"; then
      log_warning "⚠️ PM2 permission issues detected - attempting fix..."
      sudo chown -R ubuntu:ubuntu /home/<USER>/.pm2/ 2>/dev/null || true
      pm2 kill 2>/dev/null || true
      sleep 2
      pm2_status_output=$(pm2 status 2>&1 || echo "")
    fi

    # Check for version mismatch (critical issue from backup script)
    if echo "$pm2_status_output" | grep -q "In-memory PM2 is out-of-date"; then
      log_warning "⚠️ PM2 version mismatch detected - updating PM2..."

      # Update PM2 to sync versions with error handling
      log_info "Running PM2 update to synchronize versions..."
      if pm2 update >>"$LOG_FILE" 2>&1; then
        log_success "✅ PM2 updated successfully - versions synchronized"
      else
        log_warning "⚠️ PM2 update failed, trying alternative approach..."
        pm2 kill 2>/dev/null || true
        sleep 2
        pm2 ping 2>/dev/null || true
        log_info "PM2 daemon restarted - version should now be synchronized"
      fi
    else
      log_success "✅ PM2 versions are synchronized"
    fi

    return 0
  fi

  # Install PM2 globally with enhanced error handling
  log_info "Installing PM2 globally with timeout protection..."
  if ! timeout 120 npm install -g pm2 >>"$LOG_FILE" 2>&1; then
    log_warning "⚠️ PM2 global installation failed or timed out, trying alternative method..."
    # Try with sudo if needed
    if ! timeout 120 sudo npm install -g pm2 >>"$LOG_FILE" 2>&1; then
      log_error "❌ PM2 installation failed completely after timeout"
      return 1
    fi
  fi

  # Verify PM2 installation
  if command -v pm2 >/dev/null 2>&1; then
    log_success "✅ PM2 installed successfully: $(pm2 -v)"
  else
    log_error "❌ PM2 installation verification failed"
    return 1
  fi

  # Initial PM2 setup
  log_info "Initializing PM2 for ubuntu user..."
  sudo -u ubuntu pm2 ping >/dev/null 2>&1 || true

  log_success "✅ PM2 installation and setup completed"
  return 0
}

fix_pm2_permissions() {
  log_info "🔧 Fixing PM2 permissions and configuration..."

  # Fix PM2 permissions for ubuntu user
  if [[ -d "${UBUNTU_HOME}/.pm2" ]]; then
    log_info "Fixing PM2 permissions for ubuntu user..."
    sudo chown -R ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true
    chmod -R 755 "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true
  fi

  # Check for PM2 version consistency
  local pm2_check_output
  pm2_check_output=$(pm2 status 2>&1 || echo "")

  # Handle permission issues
  if echo "$pm2_check_output" | grep -q "Permission denied"; then
    log_warning "⚠️ PM2 permission issues detected - fixing..."
    sudo chown -R ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true
    pm2 kill 2>/dev/null || true
    sleep 2
  fi

  # Check for version mismatch
  if echo "$pm2_check_output" | grep -q "In-memory PM2 is out-of-date"; then
    log_warning "⚠️ PM2 version mismatch detected - updating..."
    if pm2 update >>"$LOG_FILE" 2>&1; then
      log_success "✅ PM2 updated successfully"
    else
      log_warning "⚠️ PM2 update failed, trying daemon restart..."
      pm2 kill 2>/dev/null || true
      sleep 2
      pm2 ping 2>/dev/null || true
    fi
  fi

  log_success "✅ PM2 permissions and configuration fixed"
  return 0
}

create_pm2_ecosystem() {
  log_info "📝 Creating PM2 ecosystem configuration with CORS environment variables..."

  # Determine production domain (use environment variable if set, otherwise default)
  local production_domain="${PRODUCTION_DOMAIN:-truckhaul.top}"
  log_info "🌐 Using production domain: $production_domain"

  # CRITICAL: Create ecosystem.config.js with NGINX_PROXY_MODE environment variables
  # This ensures Express.js CORS is disabled after VPS restart to prevent duplicate headers
  # Generate the file with proper variable substitution
  log_info "🔧 Generating ecosystem.config.js with domain: $production_domain"

  cat > "$APP_DIR/ecosystem.config.js" << 'ECOSYSTEM_EOF'
/**
 * PM2 Ecosystem Configuration for Hauling QR Trip System
 * Optimized for production deployment with dynamic domain configuration
 *
 * Features:
 * - Dynamic domain configuration via environment variables
 * - CORS environment variable persistence
 * - Cluster mode with optimal worker count
 * - Log rotation and management
 * - Memory and CPU optimization
 * - Health monitoring and auto-restart
 */

module.exports = {
  apps: [{
    // Application Configuration
    name: 'hauling-qr-server',
    script: './server/server.js',
    cwd: '/var/www/hauling-qr-system',

    // Cluster Configuration - Optimized for 4 vCPU VPS
    instances: process.env.NODE_ENV === 'production' ? 4 : 1,
    exec_mode: process.env.NODE_ENV === 'production' ? 'cluster' : 'fork',

    // Environment Variables - Development
    env: {
      NODE_ENV: 'development',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,

      // CORS Configuration - Development (Express.js handles CORS)
      NGINX_PROXY_MODE: 'false',
      EXPRESS_CORS_DISABLED: 'false',
      CORS_HANDLED_BY_NGINX: 'false',

      // Dynamic Domain Configuration
      PRODUCTION_DOMAIN: process.env.PRODUCTION_DOMAIN || 'localhost',
      API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:8080',
      FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:3000',
      CLIENT_URL: process.env.CLIENT_URL || 'http://localhost:3000',

      // Database Configuration
      DB_HOST: 'localhost',
      DB_PORT: '5432',
      DB_NAME: 'hauling_qr_system',
      DB_USER: 'postgres',
      DB_PASSWORD: 'PostgreSQLPassword123',

      // JWT Configuration
      JWT_SECRET: 'hauling_qr_jwt_secret_2025_secure_key_for_development',

      // Logging Configuration
      LOG_LEVEL: 'info',
      DEV_ENABLE_CORS_LOGGING: 'true',
      CORS_DEBUG_MODE: 'false',
      SUPPRESS_CORS_CONFIG_MESSAGES: 'false'
    },

    // Environment Variables - Production
    env_production: {
      NODE_ENV: 'production',
      HTTPS_PORT: 8443,
      BACKEND_HTTP_PORT: 8080,
      ENABLE_HTTPS: false,

      // CRITICAL: CORS Configuration - NGINX Proxy Mode (prevents duplicate headers)
      // These variables disable Express.js CORS when behind NGINX proxy
      // This prevents duplicate Access-Control-Allow-Origin headers that cause browser rejection
      NGINX_PROXY_MODE: 'true',
      EXPRESS_CORS_DISABLED: 'true',
      CORS_HANDLED_BY_NGINX: 'true',

      // Dynamic Domain Configuration (uses environment variables with fallbacks)
      PRODUCTION_DOMAIN: '${production_domain}',
      API_BASE_URL: 'https://api.${production_domain}',
      FRONTEND_URL: 'https://${production_domain}',
      CLIENT_URL: 'https://${production_domain}',

      // Database Configuration
      DB_HOST: 'localhost',
      DB_PORT: '5432',
      DB_NAME: 'hauling_qr_system',
      DB_USER: 'postgres',
      DB_PASSWORD: 'PostgreSQLPassword123',

      // JWT Configuration
      JWT_SECRET: 'hauling_qr_jwt_secret_2025_secure_key_for_production',

      // Server Configuration
      SERVER_PORT: '8080',
      PORT: '8080',

      // Security Configuration
      SECURE_COOKIES: 'true',

      // Production Logging Configuration - CRITICAL FOR LOG REDUCTION
      LOG_LEVEL: 'warn',
      LOG_LEVEL_PRODUCTION: 'warn',
      DEV_ENABLE_CORS_LOGGING: 'false',
      CORS_DEBUG_MODE: 'false',
      SUPPRESS_CORS_CONFIG_MESSAGES: 'true',
      SUPPRESS_CORS_REQUEST_LOGS: 'true',
      SUPPRESS_CORS_PRODUCTION_LOGS: 'true',

      // Production Monitoring Configuration
      MONITORING_DEDUPLICATION_ENABLED: 'true',
      MONITORING_DEDUPLICATION_WINDOW_MS: '600000', // 10 minutes in production
      MONITORING_MAX_DUPLICATES: '1', // Only allow 1 duplicate in production
      CORS_LOG_THROTTLE_MINUTES: '10', // Longer throttle in production

      // Performance Optimization
      SUPPRESS_DATABASE_QUERY_LOGS: 'true',
      SUPPRESS_SYNC_SUCCESS_MESSAGES: 'true',
      SUPPRESS_CLIENT_AUTH_MESSAGES: 'true',
      SUPPRESS_CONNECTION_MESSAGES: 'true',
      SUPPRESS_SHIFT_QUERY_DEBUG: 'true',
      SUPPRESS_SHIFT_COMPLETION_MESSAGES: 'true',
      SUPPRESS_DRIVER_SYNC_LOGS: 'true'
    },

    // Alternative: Load .env file as fallback
    env_file: '/var/www/hauling-qr-system/.env',

    // Logging Configuration with Rotation
    log_file: '/home/<USER>/.pm2/logs/hauling-qr-server.log',
    out_file: '/home/<USER>/.pm2/logs/hauling-qr-server-out.log',
    error_file: '/home/<USER>/.pm2/logs/hauling-qr-server-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,

    // Log Rotation Configuration - CRITICAL FOR DISK SPACE MANAGEMENT
    max_memory_restart: '1500M', // Restart if memory exceeds 1.5GB

    // Performance Optimization
    node_args: [
      '--max-old-space-size=1024',  // Limit heap to 1GB
      '--optimize-for-size'         // Optimize for memory usage
    ],

    // Health Monitoring
    health_check_grace_period: 3000,

    // Auto-restart Configuration
    autorestart: true,
    watch: false, // Disable file watching in production
    max_restarts: 10,
    min_uptime: '10s',

    // Advanced Options
    source_map_support: false,
    disable_trace: true,

    // Kill Timeout
    kill_timeout: 5000,

    // Listen Timeout
    listen_timeout: 8000,

    // Startup Delay
    restart_delay: 4000
  }]
};
ECOSYSTEM_EOF

  # Now substitute the variables in the generated file
  sed -i "s/\${production_domain}/${production_domain}/g" "$APP_DIR/ecosystem.config.js"

  # Set proper permissions
  sudo chown ubuntu:ubuntu "$APP_DIR/ecosystem.config.js"
  sudo chmod 644 "$APP_DIR/ecosystem.config.js"

  log_success "✅ PM2 ecosystem configuration created"
  return 0
}

ensure_app_directories() {
  log_info "📁 Ensuring application directories exist..."

  # Create necessary directories
  sudo mkdir -p "$APP_DIR/server/logs" "$APP_DIR/server/uploads"

  # CRITICAL FIX: Set ubuntu:ubuntu ownership for server directories
  # This prevents EACCES errors when Node.js application tries to write logs
  sudo chown -R ubuntu:ubuntu "$APP_DIR/server"
  sudo chmod -R 755 "$APP_DIR/server"

  # Ensure log directory has proper permissions for Node.js application
  sudo chown -R ubuntu:ubuntu "$APP_DIR/server/logs"
  sudo chmod -R 755 "$APP_DIR/server/logs"

  log_success "✅ Application directories created with ubuntu user ownership"
  return 0
}

create_rate_limit_fix() {
  log_info "🔧 Skipping rate-limit-fix creation - using native express-rate-limit..."

  # This function now does nothing - we use the native express-rate-limit module
  # The server.js file already has the correct import: require('express-rate-limit')
  # No modifications needed - just ensure dependencies are installed via npm install

  log_success "✅ Using native express-rate-limit module"
  return 0
}

install_systemd_service() {
  log_info "🔧 Installing systemd service for complete stack management..."

  local service_file="/etc/systemd/system/hauling-qr-system.service"
  local template_file="$(dirname "$0")/templates/hauling-qr-system.service"

  # Check if template file exists
  if [[ ! -f "$template_file" ]]; then
    log_warning "⚠️ Systemd service template not found at: $template_file"
    log_info "Checking alternative template location..."

    # Try alternative path relative to script directory
    local alt_template_file="$(cd "$(dirname "$0")" && pwd)/templates/hauling-qr-system.service"
    if [[ -f "$alt_template_file" ]]; then
      template_file="$alt_template_file"
      log_info "Found template at alternative location: $template_file"
    else
      log_error "❌ Systemd service template not found in any expected location"
      log_info "Expected locations:"
      log_info "  - $(dirname "$0")/templates/hauling-qr-system.service"
      log_info "  - $alt_template_file"
      return 1
    fi
  fi

  # CRITICAL: Create a permanent copy of the systemd service template
  # This ensures the template survives Phase 8 cleanup operations
  local permanent_template="/var/www/hauling-qr-system/hauling-qr-system.service.template"
  log_info "Creating permanent copy of systemd service template..."
  if cp "$template_file" "$permanent_template"; then
    log_success "✅ Permanent systemd service template created: $permanent_template"
  else
    log_warning "⚠️ Failed to create permanent template copy, using original"
  fi

  # CRITICAL: Detect actual Node.js installation path for robust systemd service
  log_info "Detecting Node.js installation path for systemd service..."
  local node_path=""
  local pm2_path=""

  # Try to find Node.js through NVM
  if [[ -f "/home/<USER>/.nvm/nvm.sh" ]]; then
    # Source NVM and get current Node.js path
    node_path=$(sudo -u ubuntu bash -c 'source /home/<USER>/.nvm/nvm.sh && which node' 2>/dev/null || echo "")
    pm2_path=$(sudo -u ubuntu bash -c 'source /home/<USER>/.nvm/nvm.sh && which pm2' 2>/dev/null || echo "")
  fi

  # Fallback to system-wide installation
  if [[ -z "$node_path" ]]; then
    node_path=$(which node 2>/dev/null || echo "")
    pm2_path=$(which pm2 2>/dev/null || echo "")
  fi

  if [[ -n "$node_path" && -n "$pm2_path" ]]; then
    log_success "✅ Node.js detected at: $node_path"
    log_success "✅ PM2 detected at: $pm2_path"

    # Update the template with actual paths (create a customized version)
    local custom_template="/tmp/hauling-qr-system.service.custom"
    cp "$template_file" "$custom_template"

    # Replace the shell-based commands with direct paths for better reliability
    local node_dir=$(dirname "$node_path")
    sed -i "s|source /home/<USER>/.nvm/nvm.sh && pm2|$pm2_path|g" "$custom_template"
    sed -i "s|Environment=PATH=.*|Environment=PATH=/usr/bin:/usr/local/bin:$node_dir|g" "$custom_template"

    template_file="$custom_template"
    log_info "Using customized systemd service template with detected paths"
  else
    log_warning "⚠️ Could not detect Node.js/PM2 paths, using shell-based template"
  fi

  # Note: Service file backup removed to prevent file clutter in production

  # Copy template to system location
  log_info "Installing systemd service from template..."
  if sudo cp "$template_file" "$service_file"; then
    log_success "✅ Systemd service file installed: $service_file"
  else
    log_error "❌ Failed to install systemd service file"
    return 1
  fi

  # Set proper permissions
  sudo chmod 644 "$service_file"
  sudo chown ubuntu:ubuntu "$service_file"

  # Reload systemd daemon
  log_info "Reloading systemd daemon..."
  if sudo systemctl daemon-reload; then
    log_success "✅ Systemd daemon reloaded"
  else
    log_error "❌ Failed to reload systemd daemon"
    return 1
  fi

  # Enable the service
  log_info "Enabling hauling-qr-system service..."
  if sudo systemctl enable hauling-qr-system; then
    log_success "✅ Hauling QR system service enabled for auto-start"
  else
    log_error "❌ Failed to enable hauling-qr-system service"
    return 1
  fi

  # Validate service file
  log_info "Validating systemd service configuration..."
  if sudo systemctl status hauling-qr-system >/dev/null 2>&1 || [[ $? -eq 3 ]]; then
    # Status 3 means service is loaded but not active (which is expected)
    log_success "✅ Systemd service configuration is valid"
  else
    log_error "❌ Systemd service configuration validation failed"
    return 1
  fi

  log_success "✅ Systemd service installation completed successfully"
  log_info "📋 Service Management Commands:"
  log_info "   • Start service: sudo systemctl start hauling-qr-system"
  log_info "   • Stop service: sudo systemctl stop hauling-qr-system"
  log_info "   • Check status: sudo systemctl status hauling-qr-system"
  log_info "   • View logs: sudo journalctl -u hauling-qr-system -f"

  return 0
}

configure_pm2_service() {
  log_info "🚀 Configuring PM2 service..."

  # Change to application directory
  pushd "$APP_DIR" >/dev/null

  # Stop any existing PM2 processes
  pm2 delete hauling-qr-server 2>/dev/null || true
  pm2 kill 2>/dev/null || true

  # Ensure ecosystem.config.js exists
  if [[ ! -f "ecosystem.config.js" ]]; then
    log_error "❌ ecosystem.config.js not found in $APP_DIR"
    popd >/dev/null
    return 1
  fi

  # DEPLOYMENT OPTIMIZATION: Do not start PM2 application during installation phase
  # Application startup will be handled later after dependencies are installed
  log_info "✅ PM2 installation and configuration completed"
  log_info "📝 Application startup will be handled in later deployment phases"
  log_info "🔧 PM2 ecosystem.config.js is ready for application startup"

  # DEPLOYMENT OPTIMIZATION: PM2 save will be handled after application startup
  log_info "📝 PM2 configuration save will be handled after application startup in later phases"

  # Enable PM2 startup (systemd) for ubuntu user - CRITICAL FOR AUTO-RESTART
  log_info "🔧 Configuring PM2 startup for ubuntu user with enhanced error handling..."

  # Remove the || true to ensure startup configuration succeeds
  if pm2 startup systemd -u ubuntu --hp /home/<USER>
    log_success "✅ PM2 startup configuration successful"
  else
    log_error "❌ PM2 startup configuration failed - attempting alternative method"

    # Alternative startup configuration method
    log_info "Trying alternative PM2 startup configuration..."
    if sudo -u ubuntu pm2 startup systemd; then
      log_success "✅ Alternative PM2 startup configuration successful"
    else
      log_error "❌ All PM2 startup configuration methods failed"
      log_warning "⚠️ Manual PM2 startup configuration may be required after reboot"
    fi
  fi

  # Set PM2_HOME environment variable for ubuntu user with validation
  export PM2_HOME="/home/<USER>/.pm2"

  # Ensure PM2_HOME is set in ubuntu user's environment
  if ! grep -q "PM2_HOME" /home/<USER>/.bashrc 2>/dev/null; then
    echo 'export PM2_HOME="/home/<USER>/.pm2"' >> /home/<USER>/.bashrc
    log_info "✅ PM2_HOME environment variable added to .bashrc"
  else
    log_info "✅ PM2_HOME environment variable already configured"
  fi

  # Also set in .profile for broader compatibility
  if ! grep -q "PM2_HOME" /home/<USER>/.profile 2>/dev/null; then
    echo 'export PM2_HOME="/home/<USER>/.pm2"' >> /home/<USER>/.profile
    log_info "✅ PM2_HOME environment variable added to .profile"
  fi

  # Install systemd service for complete stack management
  install_systemd_service

  # Fix PM2 ownership issues
  log_info "🔧 Fixing PM2 ownership for ubuntu user access..."
  sudo chown ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/rpc.sock" "${UBUNTU_HOME}/.pm2/pub.sock" 2>/dev/null || true
  sudo chown -R ubuntu:ubuntu "${UBUNTU_HOME}/.pm2/" 2>/dev/null || true

  # Restart with updated environment
  pm2 restart hauling-qr-server --update-env 2>/dev/null || true
  sleep 3

  popd >/dev/null

  log_success "✅ PM2 service configured successfully"
  return 0
}

verify_pm2_service() {
  log_info "🔍 Verifying PM2 installation and configuration..."

  # Verify PM2 is installed
  if command -v pm2 >/dev/null 2>&1; then
    log_success "✅ PM2 is installed and available"
  else
    log_error "❌ PM2 is not installed or not in PATH"
    return 1
  fi

  # Verify ecosystem.config.js exists
  if [[ -f "$APP_DIR/ecosystem.config.js" ]]; then
    log_success "✅ PM2 ecosystem.config.js is configured"
  else
    log_error "❌ PM2 ecosystem.config.js not found"
    return 1
  fi

  # Verify PM2 can access the configuration
  if [[ "$EUID" -eq 0 ]]; then
    if sudo -u ubuntu bash -c "cd '$APP_DIR' && pm2 prettylist" >/dev/null 2>&1; then
      log_success "✅ PM2 is accessible for ubuntu user"
    else
      log_warning "⚠️ PM2 may have permission issues for ubuntu user"
    fi
  else
    if pm2 prettylist >/dev/null 2>&1; then
      log_success "✅ PM2 is accessible"
    else
      log_warning "⚠️ PM2 may have configuration issues"
    fi
  fi

  log_info "📝 PM2 installation verification completed"
  log_info "🚀 Application startup will be handled in later deployment phases"
  return 0
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================
main() {
  start_performance_timer "pm2-installation-total"

  log_info "🚀 Starting Intelligent PM2 Installation for Hauling QR Trip System..."
  log_info "📝 Script: $SCRIPT_NAME"
  log_info "📄 Log file: $LOG_FILE"
  log_info "🧠 Mode: Intelligent Detection with Process Preservation"

  # INTELLIGENT PRE-INSTALLATION ANALYSIS
  log_info "🔍 Phase 0: Pre-Installation PM2 Analysis"
  start_performance_timer "pm2-analysis"

  # Check if we can skip this entire phase
  if should_skip_installation "pm2-installation" "pm2_app" "$UBUNTU_HOME/.pm2" 24; then
    log_success "✅ PM2 installation is recent and valid - SKIPPING entire phase"
    end_performance_timer "pm2-analysis"
    end_performance_timer "pm2-installation-total"
    return 0
  fi

  end_performance_timer "pm2-analysis"

  # Step 1: Intelligent PM2 installation
  log_info "🔄 Phase 1: PM2 Installation (Intelligent)"
  start_performance_timer "pm2-installation"

  if ! install_pm2_intelligent; then
    log_error "❌ PM2 installation failed"
    exit 1
  fi

  end_performance_timer "pm2-installation"

  # Step 2: Intelligent PM2 permissions management
  log_info "🔐 Phase 2: PM2 Permissions (Intelligent)"
  start_performance_timer "pm2-permissions"

  if ! fix_pm2_permissions; then
    log_error "❌ PM2 permissions fix failed"
    exit 1
  fi

  end_performance_timer "pm2-permissions"

  # Step 3: Intelligent PM2 ecosystem configuration
  log_info "⚙️ Phase 3: PM2 Ecosystem Configuration (Intelligent)"
  start_performance_timer "pm2-ecosystem"

  if ! create_pm2_ecosystem; then
    log_error "❌ PM2 ecosystem creation failed"
    exit 1
  fi

  end_performance_timer "pm2-ecosystem"

  # Step 4: Intelligent application directories setup
  log_info "📁 Phase 4: Application Directories (Intelligent)"
  start_performance_timer "app-directories"

  if ! ensure_app_directories; then
    log_error "❌ Application directory setup failed"
    exit 1
  fi

  end_performance_timer "app-directories"

  # Step 5: Intelligent rate limiting fix
  log_info "🚦 Phase 5: Rate Limiting Fix (Intelligent)"
  start_performance_timer "rate-limiting"

  if ! create_rate_limit_fix; then
    log_error "❌ Rate limiting fix creation failed"
    exit 1
  fi

  end_performance_timer "rate-limiting"

  # Step 6: Intelligent PM2 service configuration
  log_info "🔧 Phase 6: PM2 Service Configuration (Intelligent)"
  start_performance_timer "pm2-service-config"

  if ! configure_pm2_service; then
    log_error "❌ PM2 service configuration failed"
    exit 1
  fi

  end_performance_timer "pm2-service-config"

  # Step 7: Comprehensive PM2 service verification
  log_info "✅ Phase 7: PM2 Service Verification (Comprehensive)"
  start_performance_timer "pm2-verification"

  if ! verify_pm2_service; then
    log_error "❌ PM2 service verification failed"
    exit 1
  fi

  end_performance_timer "pm2-verification"

  end_performance_timer "pm2-installation-total"

  # Generate performance report
  generate_performance_report

  log_success "🎉 Intelligent PM2 Installation completed successfully!"
  log_info "📋 Installation Summary:"
  log_info "   ✅ PM2 process manager: Installed and configured intelligently"
  log_info "   ✅ Ecosystem configuration: Created with environment variables"
  log_info "   ✅ Application directories: Set up with proper permissions"
  log_info "   ✅ Rate limiting fix: Applied for production stability"
  log_info "   ✅ Systemd service: Installed and enabled for auto-start"
  log_info "   ✅ PM2 startup: Configured for ubuntu user persistence"
  log_info "   ✅ Service verification: Started and health-checked"

  # Create intelligent completion marker
  create_phase_completion_marker "pm2-complete" "intelligent-install-$(date +%s)"

  return 0
}

# Execute main function
main "$@"
