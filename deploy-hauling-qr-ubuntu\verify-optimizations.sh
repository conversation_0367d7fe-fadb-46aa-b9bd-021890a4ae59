#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - OPTIMIZATION VERIFICATION SCRIPT
# =============================================================================
# Quick verification script to check if optimizations are actually applied
# Run this after Phase 11 to verify service configurations
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 HAULING QR TRIP SYSTEM - OPTIMIZATION VERIFICATION"
echo "====================================================="

# Check PostgreSQL optimizations
log_info "🐘 Checking PostgreSQL optimizations..."

if systemctl is-active --quiet postgresql; then
  log_success "PostgreSQL is running"
  
  # Check key parameters
  shared_buffers=$(sudo -u postgres psql -t -c "SHOW shared_buffers;" 2>/dev/null | xargs || echo "unknown")
  max_connections=$(sudo -u postgres psql -t -c "SHOW max_connections;" 2>/dev/null | xargs || echo "unknown")
  effective_cache_size=$(sudo -u postgres psql -t -c "SHOW effective_cache_size;" 2>/dev/null | xargs || echo "unknown")
  
  echo "  - shared_buffers: $shared_buffers (expected: 2GB)"
  echo "  - max_connections: $max_connections (expected: 200)"
  echo "  - effective_cache_size: $effective_cache_size (expected: 6GB)"
  
  if [[ "$shared_buffers" == "2GB" ]]; then
    log_success "✅ PostgreSQL shared_buffers optimized"
  else
    log_warning "⚠️ PostgreSQL shared_buffers not optimized"
  fi
else
  log_error "❌ PostgreSQL is not running"
fi

echo ""

# Check PM2 optimizations
log_info "⚡ Checking PM2 optimizations..."

if command -v pm2 >/dev/null 2>&1; then
  log_success "PM2 is installed"
  
  # Check running instances
  pm2_instances=$(pm2 list | grep -c "online" || echo "0")
  echo "  - Running instances: $pm2_instances (expected: 4)"
  
  if [[ "$pm2_instances" == "4" ]]; then
    log_success "✅ PM2 cluster optimized with 4 instances"
  else
    log_warning "⚠️ PM2 not running with expected 4 instances"
  fi
  
  # Check ecosystem config
  if [[ -f "/var/www/hauling-qr-system/ecosystem.config.js" ]]; then
    log_success "✅ PM2 ecosystem configuration exists"
  else
    log_warning "⚠️ PM2 ecosystem configuration not found"
  fi
else
  log_error "❌ PM2 is not installed"
fi

echo ""

# Check NGINX optimizations
log_info "🌐 Checking NGINX optimizations..."

if systemctl is-active --quiet nginx; then
  log_success "NGINX is running"
  
  # Check configuration
  if nginx -t >/dev/null 2>&1; then
    log_success "✅ NGINX configuration is valid"
  else
    log_error "❌ NGINX configuration has errors"
  fi
  
  # Check key parameters in config
  if grep -q "worker_processes 4" /etc/nginx/nginx.conf; then
    log_success "✅ NGINX worker_processes optimized"
  else
    log_warning "⚠️ NGINX worker_processes not optimized"
  fi
  
  if grep -q "worker_connections 2048" /etc/nginx/nginx.conf; then
    log_success "✅ NGINX worker_connections optimized"
  else
    log_warning "⚠️ NGINX worker_connections not optimized"
  fi
else
  log_error "❌ NGINX is not running"
fi

echo ""

# Check system parameters
log_info "🔧 Checking system parameters..."

swappiness=$(sysctl -n vm.swappiness)
file_max=$(sysctl -n fs.file-max)
somaxconn=$(sysctl -n net.core.somaxconn)

echo "  - vm.swappiness: $swappiness (expected: 10)"
echo "  - fs.file-max: $file_max (expected: 2097152)"
echo "  - net.core.somaxconn: $somaxconn (expected: 65535)"

if [[ "$swappiness" == "10" ]]; then
  log_success "✅ System swappiness optimized"
else
  log_warning "⚠️ System swappiness not optimized"
fi

if [[ "$file_max" == "2097152" ]]; then
  log_success "✅ System file-max optimized"
else
  log_warning "⚠️ System file-max not optimized"
fi

if [[ "$somaxconn" == "65535" ]]; then
  log_success "✅ System somaxconn optimized"
else
  log_warning "⚠️ System somaxconn not optimized"
fi

echo ""
echo "🎯 VERIFICATION COMPLETE"
echo "========================"

# Summary
total_checks=9
passed_checks=0

# Count successful optimizations
[[ "$shared_buffers" == "2GB" ]] && ((passed_checks++))
[[ "$pm2_instances" == "4" ]] && ((passed_checks++))
[[ -f "/var/www/hauling-qr-system/ecosystem.config.js" ]] && ((passed_checks++))
systemctl is-active --quiet nginx && ((passed_checks++))
nginx -t >/dev/null 2>&1 && ((passed_checks++))
grep -q "worker_processes 4" /etc/nginx/nginx.conf && ((passed_checks++))
[[ "$swappiness" == "10" ]] && ((passed_checks++))
[[ "$file_max" == "2097152" ]] && ((passed_checks++))
[[ "$somaxconn" == "65535" ]] && ((passed_checks++))

echo "📊 Optimization Status: $passed_checks/$total_checks checks passed"

if [[ $passed_checks -eq $total_checks ]]; then
  log_success "🎉 ALL OPTIMIZATIONS SUCCESSFULLY APPLIED!"
elif [[ $passed_checks -ge 6 ]]; then
  log_warning "⚠️ Most optimizations applied, some issues need attention"
else
  log_error "❌ Multiple optimization issues detected"
fi

echo ""
echo "💡 To fix issues, re-run: sudo ./11_service-optimization.sh"
