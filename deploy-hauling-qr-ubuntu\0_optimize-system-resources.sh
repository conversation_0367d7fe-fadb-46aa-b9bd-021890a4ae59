#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - SYSTEM-LEVEL RESOURCE OPTIMIZATION (Phase 0)
# =============================================================================
# System-Level Resource Optimization for 4 vCPU / 8GB RAM VPS
# - System kernel parameters (vm.swappiness=10, fs.file-max, net.core.somaxconn)
# - Swap memory configuration and optimization
# - File descriptor limits and process limits
# - Network stack tuning for high-concurrency applications
# - Memory management parameters
#
# NOTE: Service-specific optimizations (PostgreSQL, PM2, NGINX) are handled
# in Phase 11 (11_service-optimization.sh) after all services are installed.
#
# Version: 2.0.0 - Restructured for system-level only
# =============================================================================

set -euo pipefail

# Load shared configuration and intelligent deployment framework
readonly OPTIMIZATION_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly SHARED_CONFIG="${OPTIMIZATION_SCRIPT_DIR}/shared-config.sh"
readonly INTELLIGENT_FRAMEWORK="${OPTIMIZATION_SCRIPT_DIR}/intelligent-deployment-framework.sh"

if [[ -f "$SHARED_CONFIG" ]]; then
  source "$SHARED_CONFIG"
else
  echo "❌ ERROR: Shared configuration not found: $SHARED_CONFIG"
  exit 1
fi

if [[ -f "$INTELLIGENT_FRAMEWORK" ]]; then
  source "$INTELLIGENT_FRAMEWORK"
else
  echo "❌ ERROR: Intelligent deployment framework not found: $INTELLIGENT_FRAMEWORK"
  exit 1
fi

# =============================================================================
# OPTIMIZATION CONFIGURATION
# =============================================================================

# System specifications for 4 vCPU / 8GB RAM VPS
readonly SYSTEM_VCPUS=4
readonly SYSTEM_RAM_GB=8
readonly SYSTEM_RAM_MB=$((SYSTEM_RAM_GB * 1024))

# System-level optimization parameters (calculated for 4 vCPU / 8GB RAM)
# These parameters optimize the kernel and system resources for high-performance applications

# System kernel parameters for high-performance applications
readonly VM_SWAPPINESS=10                  # Use swap only during memory spikes
readonly VM_DIRTY_RATIO=15                 # Percentage of memory for dirty pages
readonly VM_DIRTY_BACKGROUND_RATIO=5       # Background writeback threshold
readonly FS_FILE_MAX=2097152               # Maximum file descriptors system-wide
readonly USER_FILE_LIMIT=65536             # File descriptors per user process
readonly NET_CORE_SOMAXCONN=65535          # Maximum socket connections
readonly NET_TCP_MAX_SYN_BACKLOG=8192      # TCP SYN backlog size
readonly NET_RMEM_MAX=134217728            # Maximum TCP receive buffer (128MB)
readonly NET_WMEM_MAX=134217728            # Maximum TCP send buffer (128MB)
readonly NET_NETDEV_MAX_BACKLOG=5000       # Network device backlog
readonly NET_CORE_NETDEV_BUDGET=600        # Network processing budget

# =============================================================================
# BACKUP AND ROLLBACK FUNCTIONS
# =============================================================================

# REMOVED: Backup file creation functionality eliminated per user request

# REMOVED: Rollback functionality eliminated per user request

# =============================================================================
# SYSTEM-LEVEL OPTIMIZATION FUNCTIONS
# =============================================================================
# Note: Service-specific optimizations (PostgreSQL, PM2, NGINX) moved to Phase 11

# PM2, PostgreSQL, and NGINX optimizations moved to Phase 11 (11_service-optimization.sh)



# =============================================================================
# SYSTEM-LEVEL OPTIMIZATION
# =============================================================================

optimize_system_parameters() {
  log_info "🔧 Optimizing system-level parameters..."

  # Kernel parameters optimization with idempotent configuration management
  log_info "🔧 Applying idempotent sysctl configuration..."

  # Define unique markers for our configuration block
  local START_MARKER="# === HAULING QR TRIP SYSTEM - SYSTEM OPTIMIZATIONS START ==="
  local END_MARKER="# === HAULING QR TRIP SYSTEM - SYSTEM OPTIMIZATIONS END ==="

  # Remove any existing Hauling QR Trip System configuration blocks
  if grep -q "$START_MARKER" /etc/sysctl.conf; then
    log_info "Removing existing Hauling QR Trip System configuration..."
    sudo sed -i "/$START_MARKER/,/$END_MARKER/d" /etc/sysctl.conf
  fi

  # Add our configuration block
  sudo tee -a /etc/sysctl.conf > /dev/null << EOF

$START_MARKER
# Applied: $(date)
# System: 4 vCPU / 8GB RAM VPS
# Version: Phase 0 System-Level Optimizations

# Memory Management (optimized for 8GB RAM)
vm.swappiness = $VM_SWAPPINESS
vm.dirty_ratio = $VM_DIRTY_RATIO
vm.dirty_background_ratio = $VM_DIRTY_BACKGROUND_RATIO
vm.overcommit_memory = 2
vm.overcommit_ratio = 80

# File System (optimized for high-concurrency applications)
fs.file-max = $FS_FILE_MAX

# Network Stack Optimization (optimized for QR scanning and API operations)
net.core.somaxconn = $NET_CORE_SOMAXCONN
net.ipv4.tcp_max_syn_backlog = $NET_TCP_MAX_SYN_BACKLOG
net.core.rmem_max = $NET_RMEM_MAX
net.core.wmem_max = $NET_WMEM_MAX
net.core.netdev_max_backlog = $NET_NETDEV_MAX_BACKLOG
net.core.netdev_budget = $NET_CORE_NETDEV_BUDGET
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_probes = 7
net.ipv4.tcp_keepalive_intvl = 30
net.ipv4.tcp_rmem = 4096 87380 $NET_RMEM_MAX
net.ipv4.tcp_wmem = 4096 65536 $NET_WMEM_MAX

# Security
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
$END_MARKER
EOF

  # Apply kernel parameters
  sudo sysctl -p

  # User limits optimization with idempotent configuration management
  log_info "🔧 Applying idempotent limits.conf configuration..."

  # Define unique markers for limits configuration
  local LIMITS_START_MARKER="# === HAULING QR TRIP SYSTEM - USER LIMITS START ==="
  local LIMITS_END_MARKER="# === HAULING QR TRIP SYSTEM - USER LIMITS END ==="

  # Remove any existing Hauling QR Trip System limits configuration
  if grep -q "$LIMITS_START_MARKER" /etc/security/limits.conf; then
    log_info "Removing existing Hauling QR Trip System limits configuration..."
    sudo sed -i "/$LIMITS_START_MARKER/,/$LIMITS_END_MARKER/d" /etc/security/limits.conf
  fi

  # Add our limits configuration block
  sudo tee -a /etc/security/limits.conf > /dev/null << EOF

$LIMITS_START_MARKER
# Applied: $(date)
# System: 4 vCPU / 8GB RAM VPS
$UBUNTU_USER soft nofile $USER_FILE_LIMIT
$UBUNTU_USER hard nofile $USER_FILE_LIMIT
$UBUNTU_USER soft nproc $USER_FILE_LIMIT
$UBUNTU_USER hard nproc $USER_FILE_LIMIT
www-data soft nofile $USER_FILE_LIMIT
www-data hard nofile $USER_FILE_LIMIT
postgres soft nofile $USER_FILE_LIMIT
postgres hard nofile $USER_FILE_LIMIT
$LIMITS_END_MARKER
EOF

  # Disable transparent huge pages (can cause performance issues)
  echo never | sudo tee /sys/kernel/mm/transparent_hugepage/enabled > /dev/null
  echo never | sudo tee /sys/kernel/mm/transparent_hugepage/defrag > /dev/null

  log_success "✅ System parameters optimized"
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

# Service validation functions moved to Phase 11 (11_service-optimization.sh)

validate_system_optimization() {
  log_info "🔍 Validating system optimization..."

  # Check kernel parameters
  local current_swappiness
  current_swappiness=$(cat /proc/sys/vm/swappiness)

  if [[ "$current_swappiness" == "$VM_SWAPPINESS" ]]; then
    log_success "✅ System swappiness: $current_swappiness"
  else
    log_warning "⚠️ System swappiness not applied: $current_swappiness (expected: $VM_SWAPPINESS)"
  fi

  # Check file limits
  local current_file_max
  current_file_max=$(cat /proc/sys/fs/file-max)

  if [[ "$current_file_max" -ge "$FS_FILE_MAX" ]]; then
    log_success "✅ System file-max: $current_file_max"
  else
    log_warning "⚠️ System file-max may need adjustment: $current_file_max (expected: >= $FS_FILE_MAX)"
  fi

  return 0
}

# =============================================================================
# MAIN OPTIMIZATION FUNCTION
# =============================================================================

main() {
  log_info "🚀 Starting System Resource Optimization (Phase 0)"
  log_info "📅 Started at: $(date)"
  log_info "🖥️ System: $SYSTEM_VCPUS vCPU / ${SYSTEM_RAM_GB}GB RAM VPS"

  # Define log file for this script
  local LOG_FILE="${LOG_DIR}/optimization-$(date +%Y%m%d-%H%M%S).log"
  log_info "📝 Log file: $LOG_FILE"

  # Initialize intelligent deployment tracking
  init_intelligent_progress_tracking "system-optimization" "essential"

  # Check root privileges
  if [[ $EUID -ne 0 ]]; then
    log_error "❌ This script must be run as root (use sudo)"
    exit 1
  fi

  # REMOVED: Backup and rollback functionality eliminated per user request

  # Step 1: Optimize system-level parameters
  if ! optimize_system_parameters; then
    log_error "❌ System parameters optimization failed"
    exit 1
  fi

  # Step 2: Validate system optimizations
  log_info "🔍 Validating system-level optimizations..."
  validate_system_optimization || log_warning "⚠️ System validation had issues"

  # Update deployment progress
  update_deployment_progress "system-optimization" "completed" "essential" "All optimizations applied successfully"

  # REMOVED: Error trap clearing (no longer needed)

  log_success "✅ System-Level Resource Optimization completed successfully"
  log_info "📊 System-level performance improvements applied:"
  log_info "   • Memory Management: vm.swappiness=$VM_SWAPPINESS, optimized dirty page ratios"
  log_info "   • File System: fs.file-max=$FS_FILE_MAX file descriptors"
  log_info "   • Network Stack: net.core.somaxconn=$NET_CORE_SOMAXCONN, TCP buffers optimized"
  log_info "   • User Limits: $USER_FILE_LIMIT file descriptors per process"
  log_info "   • Security: Kernel hardening parameters applied"
  log_info ""
  log_info "🔄 Service-specific optimizations (PostgreSQL, PM2, NGINX) will be applied in Phase 11"
}

# Execute main function
main "$@"
