import axios from 'axios';
import toast from 'react-hot-toast';

// Import network utilities for dynamic detection
import { getApiBaseUrl as getApiBaseUrlUtil } from '../utils/network-utils';

// Determine API base URL with automatic detection
const getApiBaseUrl = () => {
  return getApiBaseUrlUtil();
};

// Create axios instance for driver operations
const apiBaseUrl = getApiBaseUrl();
console.log('🔧 DriverAPI Base URL:', apiBaseUrl);
console.log('🔧 Current hostname:', window.location.hostname);
console.log('🔧 Current protocol:', window.location.protocol);

const driverAPI = axios.create({
  baseURL: apiBaseUrl,
  timeout: 30000, // Longer timeout for mobile devices
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for driver API (no auth token needed for public endpoints)
driverAPI.interceptors.request.use(
  (config) => {
    // Only add auth token for admin endpoints
    if (config.url?.includes('/driver-admin/')) {
      const token = localStorage.getItem('hauling_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
driverAPI.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      
      // Handle specific driver API errors
      if (status === 400) {
        // Validation errors - don't show toast, let component handle
        console.warn('Driver API validation error:', data.message);
      } else if (status === 401 && error.config.url?.includes('/driver-admin/')) {
        // Only handle auth errors for admin endpoints
        localStorage.removeItem('hauling_token');
        localStorage.removeItem('hauling_user');
        toast.error('Session expired. Please login again.');
        window.location.href = '/login';
      } else if (status === 429) {
        toast.error('Too many requests. Please wait a moment before trying again.');
      } else if (status >= 500) {
        toast.error('Server error. Please try again later.');
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    }
    
    return Promise.reject(error);
  }
);

// Retry utility for rate limited requests
const retryWithBackoff = async (fn, retries = 3, delay = 1000) => {
  try {
    return await fn();
  } catch (error) {
    if (error.response?.status === 429 && retries > 0) {
      const retryAfter = error.response.headers['retry-after'];
      const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : delay;
      
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return retryWithBackoff(fn, retries - 1, delay * 2);
    }
    throw error;
  }
};

/**
 * Driver Connect Operations (Public API - No Authentication Required)
 */

/**
 * Get driver status by employee ID
 * @param {string} employeeId - Driver employee ID
 * @returns {Promise<Object>} Driver status information
 */
export const getDriverStatus = async (employeeId) => {
  return retryWithBackoff(() => driverAPI.get(`/driver/status/${employeeId}`));
};

/**
 * Get driver's active trip by employee ID
 * @param {string} employeeId - Driver employee ID
 * @returns {Promise<Object>} Active trip information
 */
export const getDriverActiveTrip = async (employeeId) => {
  return retryWithBackoff(() => driverAPI.get(`/driver/active-trip/${employeeId}`));
};

/**
 * Get truck status by truck number
 * @param {string} truckNumber - Truck number (e.g., "DT-100")
 * @returns {Promise<Object>} Truck status information
 */
export const getTruckStatus = async (truckNumber) => {
  return retryWithBackoff(() => driverAPI.get(`/driver/truck-status/${truckNumber}`));
};

/**
 * Process driver-truck connection (check-in/check-out)
 * @param {Object} connectionData - Connection data
 * @param {string|Object} connectionData.driver_qr_data - Driver QR code data
 * @param {string|Object} connectionData.truck_qr_data - Truck QR code data
 * @param {string} [connectionData.action] - Optional action ('check_in' or 'check_out')
 * @returns {Promise<Object>} Connection result
 */
export const processDriverConnect = async (connectionData) => {
  return retryWithBackoff(() => driverAPI.post('/driver/connect', connectionData));
};

/**
 * Health check for driver connect system
 * @returns {Promise<Object>} Health status
 */
export const getDriverConnectHealth = async () => {
  return driverAPI.get('/driver/health');
};

/**
 * Driver Admin Operations (Authenticated API - Requires Login)
 */

/**
 * Generate QR code for a driver
 * @param {number} driverId - Driver ID
 * @returns {Promise<Object>} Generated QR code data
 */
export const generateDriverQR = async (driverId) => {
  return retryWithBackoff(() => driverAPI.post(`/driver-admin/generate-qr/${driverId}`));
};

/**
 * Get driver QR code information
 * @param {number} driverId - Driver ID
 * @returns {Promise<Object>} QR code information
 */
export const getDriverQRInfo = async (driverId) => {
  return driverAPI.get(`/driver-admin/qr-info/${driverId}`);
};

/**
 * Get driver attendance records with filtering
 * @param {Object} filters - Filter options
 * @param {number} [filters.driver_id] - Filter by driver ID
 * @param {string} [filters.date_from] - Start date (YYYY-MM-DD)
 * @param {string} [filters.date_to] - End date (YYYY-MM-DD)
 * @param {number} [filters.truck_id] - Filter by truck ID
 * @param {string} [filters.status] - Filter by status ('all', 'active', 'completed')
 * @param {number} [filters.limit] - Number of records per page (default: 50)
 * @param {number} [filters.offset] - Pagination offset (default: 0)
 * @param {string} [filters.sort_by] - Sort column (default: 'start_date')
 * @param {string} [filters.sort_order] - Sort order ('asc' or 'desc', default: 'desc')
 * @returns {Promise<Object>} Attendance records with pagination
 */
export const getAttendanceRecords = async (filters = {}) => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, value);
    }
  });

  return retryWithBackoff(() => 
    driverAPI.get(`/driver-admin/attendance?${params.toString()}`)
  );
};

/**
 * Get attendance summary for a specific period
 * @param {Object} options - Summary options
 * @param {string} options.period - Period type ('daily', 'weekly', 'monthly')
 * @param {number} [options.driver_id] - Filter by driver ID
 * @param {string} [options.date_from] - Start date (YYYY-MM-DD)
 * @param {string} [options.date_to] - End date (YYYY-MM-DD)
 * @returns {Promise<Object>} Attendance summary
 */
export const getAttendanceSummary = async (options = {}) => {
  const params = new URLSearchParams();
  
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, value);
    }
  });

  return retryWithBackoff(() => 
    driverAPI.get(`/driver-admin/attendance-summary?${params.toString()}`)
  );
};

/**
 * Export attendance data for payroll processing
 * @param {Object} filters - Export filters (same as getAttendanceRecords)
 * @returns {Promise<Object>} Formatted data for export
 */
export const exportAttendanceData = async (filters = {}) => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, value);
    }
  });

  return retryWithBackoff(() => 
    driverAPI.get(`/driver-admin/export-attendance?${params.toString()}`)
  );
};

/**
 * Manual checkout for emergency situations
 * @param {number} shiftId - Shift ID to checkout
 * @param {Object} checkoutData - Checkout data
 * @param {string} checkoutData.reason - Reason for manual checkout
 * @param {string} [checkoutData.notes] - Additional notes
 * @returns {Promise<Object>} Checkout result
 */
export const manualCheckout = async (shiftId, checkoutData) => {
  return retryWithBackoff(() => 
    driverAPI.post(`/driver-admin/manual-checkout/${shiftId}`, checkoutData)
  );
};

/**
 * Health check for driver admin system
 * @returns {Promise<Object>} Health status
 */
export const getDriverAdminHealth = async () => {
  return driverAPI.get('/driver-admin/health');
};

/**
 * Utility Functions
 */

/**
 * Format duration from milliseconds to human readable format
 * @param {number} durationMs - Duration in milliseconds
 * @returns {string} Formatted duration (e.g., "8h 30m")
 */
export const formatDuration = (durationMs) => {
  if (!durationMs || durationMs < 0) return '0h 0m';
  
  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
  
  return `${hours}h ${minutes}m`;
};

/**
 * Format date for display
 * @param {string} dateString - Date string (YYYY-MM-DD)
 * @returns {string} Formatted date
 */
export const formatDate = (dateString) => {
  if (!dateString) return '';
  
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Format time for display
 * @param {string} timeString - Time string (HH:MM:SS)
 * @returns {string} Formatted time
 */
export const formatTime = (timeString) => {
  if (!timeString) return '';
  
  return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Validate QR code structure
 * @param {string|Object} qrData - QR code data
 * @param {string} expectedType - Expected QR code type ('driver' or 'truck')
 * @returns {Object} Validation result
 */
export const validateQRCode = (qrData, expectedType) => {
  try {
    let parsedData;
    
    if (typeof qrData === 'string') {
      parsedData = JSON.parse(qrData);
    } else {
      parsedData = qrData;
    }

    if (!parsedData || typeof parsedData !== 'object') {
      return {
        valid: false,
        error: 'QR code data is not a valid object'
      };
    }

    if (!parsedData.type || !parsedData.id) {
      return {
        valid: false,
        error: 'QR code missing required fields (type, id)'
      };
    }

    if (parsedData.type !== expectedType) {
      return {
        valid: false,
        error: `Expected ${expectedType} QR code, but got ${parsedData.type}`
      };
    }

    return {
      valid: true,
      data: parsedData
    };

  } catch (error) {
    return {
      valid: false,
      error: 'Invalid QR code format - not valid JSON'
    };
  }
};

/**
 * Get current timestamp in ISO format
 * @returns {string} Current timestamp
 */
export const getCurrentTimestamp = () => {
  return new Date().toISOString();
};

/**
 * Calculate shift duration between two timestamps
 * @param {string} startTime - Start timestamp
 * @param {string} endTime - End timestamp
 * @returns {Object} Duration information
 */
export const calculateShiftDuration = (startTime, endTime) => {
  if (!startTime || !endTime) {
    return {
      duration_ms: 0,
      duration_formatted: '0h 0m',
      hours: 0,
      minutes: 0
    };
  }

  const start = new Date(startTime);
  const end = new Date(endTime);
  const durationMs = end - start;

  if (durationMs < 0) {
    return {
      duration_ms: 0,
      duration_formatted: '0h 0m',
      hours: 0,
      minutes: 0,
      error: 'End time cannot be before start time'
    };
  }

  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

  return {
    duration_ms: durationMs,
    duration_formatted: `${hours}h ${minutes}m`,
    hours,
    minutes
  };
};

// Default export for backward compatibility
const driverAPIService = {
  // Public API
  getDriverStatus,
  getDriverActiveTrip,
  getTruckStatus,
  processDriverConnect,
  getDriverConnectHealth,

  // Admin API
  generateDriverQR,
  getDriverQRInfo,
  getAttendanceRecords,
  getAttendanceSummary,
  exportAttendanceData,
  manualCheckout,
  getDriverAdminHealth,

  // Utilities
  formatDuration,
  formatDate,
  formatTime,
  validateQRCode,
  getCurrentTimestamp,
  calculateShiftDuration
};

export default driverAPIService;