#!/bin/bash

# =============================================================================
# OPTIMIZED SYSTEM DEPENDENCIES INSTALLATION - PERFORMANCE ENHANCED
# =============================================================================
# Version: 4.0.0 - High-performance parallel installation with reduced timeouts
# Compatible with: Ubuntu 24.04 LTS, WSL Ubuntu
# Description: Optimized parallel package installation with 10-minute max timeout
# Performance: Sub-5-minute installation with parallel processing and caching
# =============================================================================

set -e  # Exit on any error

# Source shared configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/shared-config.sh" || {
  echo "ERROR: shared-config.sh not found"
  exit 1
}

# Set up logging and progress tracking
mkdir -p "$LOG_DIR" 2>/dev/null || true
export LOG_FILE="${LOG_DIR}/system-dependencies-$(date +%Y%m%d-%H%M%S).log"
export PROGRESS_FILE="${LOG_DIR}/installation-progress.json"
export RETRY_FILE="${LOG_DIR}/retry-attempts.json"

# Global variables for enhanced package management
declare -a MISSING_PACKAGES
declare -a PARALLEL_INSTALL_GROUPS

# =============================================================================
# PERFORMANCE OPTIMIZATION CONFIGURATION
# =============================================================================
readonly MAX_TOTAL_TIMEOUT=600  # 10 minutes maximum for entire phase
readonly PACKAGE_TIMEOUT=45     # 45 seconds per package (reduced from 90-180s)
readonly PARALLEL_BATCH_SIZE=4  # Install 4 packages in parallel
readonly WSL_OPTIMIZED=true     # Enable WSL-specific optimizations

# =============================================================================
# PARALLEL PACKAGE INSTALLATION SYSTEM
# =============================================================================

# Install packages in parallel batches for performance
install_packages_parallel() {
  local -a packages=("$@")
  local total_packages=${#packages[@]}

  if [[ $total_packages -eq 0 ]]; then
    log_info "No packages to install in parallel batch"
    return 0
  fi

  log_info "🚀 Installing $total_packages packages in parallel (batch size: $PARALLEL_BATCH_SIZE)"

  # Split packages into parallel batches
  local batch_count=0
  local -a current_batch=()
  local -a batch_pids=()

  for package in "${packages[@]}"; do
    current_batch+=("$package")

    # When batch is full or we've reached the end, process the batch
    if [[ ${#current_batch[@]} -eq $PARALLEL_BATCH_SIZE ]] || [[ $((batch_count * PARALLEL_BATCH_SIZE + ${#current_batch[@]})) -eq $total_packages ]]; then
      batch_count=$((batch_count + 1))
      log_info "📦 Processing batch $batch_count with ${#current_batch[@]} packages: ${current_batch[*]}"

      # Install current batch in parallel
      for pkg in "${current_batch[@]}"; do
        install_single_package_background "$pkg" &
        batch_pids+=($!)
      done

      # Wait for all packages in this batch to complete
      local batch_success=true
      for pid in "${batch_pids[@]}"; do
        if ! wait "$pid"; then
          batch_success=false
        fi
      done

      if [[ "$batch_success" == "true" ]]; then
        log_success "✅ Batch $batch_count completed successfully"
      else
        log_warning "⚠️ Some packages in batch $batch_count failed, will retry individually"
      fi

      # Reset for next batch
      current_batch=()
      batch_pids=()
    fi
  done

  log_success "🎉 Parallel installation completed for $total_packages packages"
  return 0
}

# Background installation function for parallel processing
install_single_package_background() {
  local package="$1"
  local log_file="/tmp/install_${package}_$$.log"

  # Quick check if already installed
  if dpkg -l "$package" 2>/dev/null | grep -q "^ii"; then
    log_info "✅ $package already installed (parallel check)"
    return 0
  fi

  # Fast installation with minimal timeout
  local install_cmd="apt-get install -y --no-install-recommends -o Dpkg::Options::=\"--force-confdef\" -o Dpkg::Options::=\"--force-confold\" \"$package\""

  if timeout $PACKAGE_TIMEOUT bash -c "$install_cmd" >"$log_file" 2>&1; then
    log_success "✅ $package installed successfully (parallel)"
    rm -f "$log_file"
    return 0
  else
    log_warning "⚠️ $package failed in parallel installation, will retry individually"
    cat "$log_file" >> "$LOG_FILE" 2>/dev/null || true
    rm -f "$log_file"
    return 1
  fi
}

# =============================================================================
# SIMPLIFIED PROGRESS TRACKING SYSTEM
# =============================================================================

# Initialize progress tracking
init_progress_tracking() {
  log_info "Initializing simplified progress tracking..."

  # Create simple text-based progress files
  mkdir -p "$(dirname "$PROGRESS_FILE")" 2>/dev/null || true
  touch "${PROGRESS_FILE}.completed" "${PROGRESS_FILE}.failed" "${PROGRESS_FILE}.retry"

  log_success "Progress tracking initialized"
}

# Update progress for a package
update_package_progress() {
  local package="$1"
  local status="$2"  # "completed", "failed"

  case "$status" in
    "completed")
      echo "$package" >> "${PROGRESS_FILE}.completed"
      # Remove from failed if it was there
      if [[ -f "${PROGRESS_FILE}.failed" ]]; then
        grep -v "^$package$" "${PROGRESS_FILE}.failed" > "${PROGRESS_FILE}.failed.tmp" 2>/dev/null || true
        mv "${PROGRESS_FILE}.failed.tmp" "${PROGRESS_FILE}.failed" 2>/dev/null || true
      fi
      ;;
    "failed")
      echo "$package" >> "${PROGRESS_FILE}.failed"
      ;;
  esac
}

# Check if package is already completed
is_package_completed() {
  local package="$1"
  [[ -f "${PROGRESS_FILE}.completed" ]] && grep -q "^$package$" "${PROGRESS_FILE}.completed" 2>/dev/null
}

# Get retry count for a package
get_retry_count() {
  local package="$1"
  if [[ -f "${PROGRESS_FILE}.retry" ]]; then
    grep "^$package:" "${PROGRESS_FILE}.retry" 2>/dev/null | cut -d: -f2 || echo "0"
  else
    echo "0"
  fi
}

# Increment retry count for a package
increment_retry_count() {
  local package="$1"
  local current_count=$(get_retry_count "$package")
  local new_count=$((current_count + 1))

  # Remove old entry and add new one
  if [[ -f "${PROGRESS_FILE}.retry" ]]; then
    grep -v "^$package:" "${PROGRESS_FILE}.retry" > "${PROGRESS_FILE}.retry.tmp" 2>/dev/null || true
    mv "${PROGRESS_FILE}.retry.tmp" "${PROGRESS_FILE}.retry" 2>/dev/null || true
  fi
  echo "$package:$new_count" >> "${PROGRESS_FILE}.retry"
}

# Enhanced installation verification with caching
verify_package_installation() {
  local package="$1"
  local use_cache="${2:-true}"

  # Use cached results if available and recent (within 60 seconds)
  local cache_file="/tmp/package_cache_${package}"
  if [[ "$use_cache" == "true" && -f "$cache_file" ]]; then
    local cache_age=$(($(date +%s) - $(stat -c %Y "$cache_file" 2>/dev/null || echo 0)))
    if [[ $cache_age -lt 60 ]]; then
      local cached_result=$(cat "$cache_file" 2>/dev/null || echo "false")
      [[ "$cached_result" == "true" ]] && return 0 || return 1
    fi
  fi

  # Check if package is installed via dpkg (faster than apt)
  local is_installed="false"
  if dpkg-query -W -f='${Status}' "$package" 2>/dev/null | grep -q "install ok installed"; then
    is_installed="true"
  fi

  # Cache the result
  echo "$is_installed" > "$cache_file" 2>/dev/null || true

  [[ "$is_installed" == "true" ]] && return 0 || return 1
}

# Bulk package verification for efficiency
verify_packages_bulk() {
  local packages=("$@")
  local installed_packages=()
  local missing_packages=()

  log_info "🔍 Performing bulk package verification for ${#packages[@]} packages..."

  # Get all installed packages in one command
  local installed_list
  installed_list=$(dpkg-query -W -f='${Package} ${Status}\n' 2>/dev/null | grep "install ok installed" | cut -d' ' -f1)

  # Check each package against the installed list
  for package in "${packages[@]}"; do
    if echo "$installed_list" | grep -q "^${package}$"; then
      installed_packages+=("$package")
      update_package_progress "$package" "completed"
    else
      missing_packages+=("$package")
    fi
  done

  log_info "📊 Package verification results:"
  log_info "   • Already installed: ${#installed_packages[@]} packages"
  log_info "   • Need installation: ${#missing_packages[@]} packages"

  if [[ ${#installed_packages[@]} -gt 0 ]]; then
    log_info "   • Installed packages: ${installed_packages[*]}"
  fi

  if [[ ${#missing_packages[@]} -gt 0 ]]; then
    log_info "   • Missing packages: ${missing_packages[*]}"
  fi

  # Return the missing packages via global variable
  MISSING_PACKAGES=("${missing_packages[@]}")

  return 0
}

# =============================================================================
# SIMPLIFIED PACKAGE INSTALLATION WITH RETRY LOGIC
# =============================================================================

install_single_package() {
  local package="$1"
  local current="$2"
  local total="$3"
  local is_essential="${4:-false}"

  log_info "Installing $package ($current/$total)..."

  # Check if package is already completed from previous run
  if is_package_completed "$package"; then
    log_success "$package already completed in previous run"
    return 0
  fi

  # Check if already installed via dpkg
  if verify_package_installation "$package"; then
    log_success "$package already installed"
    update_package_progress "$package" "completed"
    return 0
  fi

  # Set retry limits
  local max_retries=3
  if [[ "$is_essential" == "true" ]]; then
    max_retries=5  # More retries for essential packages
  fi

  # Set non-interactive mode
  export DEBIAN_FRONTEND=noninteractive

  # Retry loop (iterative, not recursive)
  for attempt in $(seq 1 $max_retries); do
    log_info "Installing $package (attempt $attempt/$max_retries)..."

    # Optimized timeout configuration (reduced from 90-180s to 45-60s)
    local timeout_seconds=$PACKAGE_TIMEOUT
    case "$package" in
      "build-essential") timeout_seconds=60 ;;  # Reduced from 180s
      "nginx") timeout_seconds=50 ;;            # Reduced from 120s
    esac

    # Minimal timeout increase for retries (reduced from 30s to 10s)
    timeout_seconds=$((timeout_seconds + (attempt - 1) * 10))

    # Simple installation command
    local install_cmd="apt-get install -y \
      -o Dpkg::Options::=\"--force-confdef\" \
      -o Dpkg::Options::=\"--force-confold\" \
      -o APT::Get::Assume-Yes=true \
      -o APT::Install-Recommends=false \
      -o APT::Install-Suggests=false \
      --no-install-recommends \
      \"$package\""

    # Direct installation with simple timeout
    log_info "Installing $package with ${timeout_seconds}s timeout..."
    if timeout "$timeout_seconds" bash -c "$install_cmd" >>"$LOG_FILE" 2>&1; then
      # Verify installation was successful
      if verify_package_installation "$package"; then
        log_success "$package installed successfully (attempt $attempt)"
        update_package_progress "$package" "completed"
        return 0
      else
        log_warning "$package installation completed but verification failed"
      fi
    else
      log_warning "$package installation failed or timed out (attempt $attempt/$max_retries)"
    fi

    # For essential packages, try alternative installation method
    if [[ "$is_essential" == "true" && $attempt -lt $max_retries ]]; then
      log_info "Trying alternative installation method for essential package $package"

      local alt_cmd="apt-get install -y --fix-broken \"$package\""
      if timeout $((timeout_seconds / 2)) bash -c "$alt_cmd" >>"$LOG_FILE" 2>&1; then
        if verify_package_installation "$package"; then
          log_success "$package installed via alternative method"
          update_package_progress "$package" "completed"
          return 0
        fi
      fi
    fi

    # Wait before retry (progressive backoff)
    if [[ $attempt -lt $max_retries ]]; then
      local wait_time=$((attempt * 5))
      log_info "Waiting ${wait_time}s before next retry of $package..."
      sleep $wait_time
    fi
  done

  # All attempts failed
  log_error "$package failed after $max_retries attempts"
  update_package_progress "$package" "failed"

  if [[ "$is_essential" == "true" ]]; then
    log_error "CRITICAL: Essential package $package could not be installed"
    return 1
  else
    log_warning "Optional package $package skipped after $max_retries attempts"
    return 1
  fi
}

install_system_packages() {
  log_info "Starting intelligent system packages installation with retry logic..."
  export DEBIAN_FRONTEND=noninteractive

  # Initialize progress tracking
  init_progress_tracking

  # Basic disk space check
  local available_space=$(df / | awk 'NR==2 {print int($4/1024)}')
  if [[ $available_space -lt 1024 ]]; then
    log_error "Insufficient disk space: ${available_space}MB (need 1GB+)"
    return 1
  fi
  log_info "Disk space: ${available_space}MB available"

  # Check for previous installation state
  if [[ -f "${PROGRESS_FILE}.completed" ]]; then
    local completed_count=$(wc -l < "${PROGRESS_FILE}.completed" 2>/dev/null || echo "0")
    if [[ $completed_count -gt 0 ]]; then
      log_info "Resuming installation - $completed_count packages already completed"
    fi
  fi

  # Smart package list update with retry
  log_info "Updating package lists with intelligent retry..."
  local update_attempts=0
  local max_update_attempts=3

  while [[ $update_attempts -lt $max_update_attempts ]]; do
    update_attempts=$((update_attempts + 1))
    log_info "Package list update attempt $update_attempts/$max_update_attempts"

    if timeout 60 apt-get update -qq >>"$LOG_FILE" 2>&1; then
      log_success "Package lists updated successfully"
      break
    elif [[ $update_attempts -eq $max_update_attempts ]]; then
      log_warning "Package list update failed after $max_update_attempts attempts, continuing anyway..."
    else
      log_warning "Package list update failed, retrying in 5 seconds..."
      sleep 5
    fi
  done

  # Essential packages (must install for system to work)
  local essential_packages=(
    "ca-certificates"
    "curl"
    "git"
    "lsb-release"
  )

  # Optional packages (nice to have, but deployment continues if they fail)
  local optional_packages=(
    "gnupg"
    "tzdata"
    "bc"
    "net-tools"
    "build-essential"
    "nginx"
  )

  local total_packages=$((${#essential_packages[@]} + ${#optional_packages[@]}))
  local current_package=0
  local failed_essential=()
  local failed_optional=()
  local start_time=$(date +%s)

  log_info "=== ENHANCED PACKAGE INSTALLATION PHASE ==="
  log_info "Using proven installation methods with fallback support"
  log_info "Total packages to install: $total_packages"
  log_info "Essential packages: ${#essential_packages[@]}"
  log_info "Optional packages: ${#optional_packages[@]}"
  log_info "Progress tracking: $PROGRESS_FILE"
  log_info "Retry tracking: $RETRY_FILE"
  echo ""

  # ENHANCED: Smart package detection before installation
  log_info "🔍 Performing smart package detection to optimize installation..."

  local all_packages=("${essential_packages[@]}" "${optional_packages[@]}")
  verify_packages_bulk "${all_packages[@]}"

  # MISSING_PACKAGES is set by verify_packages_bulk
  if [[ ${#MISSING_PACKAGES[@]} -eq 0 ]]; then
    log_success "✅ All packages already installed - skipping installation phase"
    log_info "� Installation optimization: 100% packages already present"
    return 0
  fi

  log_info "📦 Need to install ${#MISSING_PACKAGES[@]} missing packages: ${MISSING_PACKAGES[*]}"

  # Try high-performance parallel installation first
  log_info "🚀 Attempting high-performance parallel installation for ${#MISSING_PACKAGES[@]} missing packages..."

  # Use optimized timeout (reduced from 30s per package to 15s per package)
  local bulk_timeout=$((${#MISSING_PACKAGES[@]} * 15 + 60))  # 15s per package + 1min base (reduced from 30s + 2min)
  local max_bulk_timeout=300  # Maximum 5 minutes for bulk installation
  if [[ $bulk_timeout -gt $max_bulk_timeout ]]; then
    bulk_timeout=$max_bulk_timeout
  fi

  log_info "⏱️ Using optimized timeout: ${bulk_timeout}s for ${#MISSING_PACKAGES[@]} packages (max 5min)"

  # Try parallel installation first for better performance
  if install_packages_parallel "${MISSING_PACKAGES[@]}"; then
    log_success "✅ High-performance parallel installation completed successfully"

    # Mark missing packages as completed
    for package in "${MISSING_PACKAGES[@]}"; do
      update_package_progress "$package" "completed"
    done

    # Quick verification
    local verified_count=0
    for package in "${MISSING_PACKAGES[@]}"; do
      if verify_package_installation "$package" "false"; then
        verified_count=$((verified_count + 1))
      fi
    done

    log_success "✅ Verified $verified_count/${#MISSING_PACKAGES[@]} packages installed via parallel method"

    if [[ $verified_count -eq ${#MISSING_PACKAGES[@]} ]]; then
      log_success "✅ All missing packages installed - skipping individual installation"
      return 0
    else
      log_warning "⚠️ Some packages may need individual installation (${verified_count}/${#MISSING_PACKAGES[@]} verified)"
    fi
  else
    log_warning "⚠️ Optimized bulk installation failed or timed out"
    log_info "🔄 Falling back to individual installation with enhanced retry logic"
  fi

  # ENHANCED: Process only packages that actually need installation
  log_info "🔧 Phase 1: Installing essential packages..."

  # Filter essential packages to only those that need installation
  local essential_to_install=()
  for package in "${essential_packages[@]}"; do
    if [[ " ${MISSING_PACKAGES[*]} " =~ " ${package} " ]]; then
      essential_to_install+=("$package")
    fi
  done

  log_info "📦 Essential packages needing installation: ${#essential_to_install[@]}/${#essential_packages[@]}"

  if [[ ${#essential_to_install[@]} -eq 0 ]]; then
    log_success "✅ All essential packages already installed - skipping individual installation"
  else
    for package in "${essential_to_install[@]}"; do
      current_package=$((current_package + 1))

      log_info "--- Processing essential package $package ($current_package/$total_packages) ---"

      if ! install_single_package "$package" "$current_package" "$total_packages" "true"; then
        failed_essential+=("$package")
        log_error "❌ CRITICAL: Essential package $package failed after all retry attempts"

        # For essential packages, ask user what to do
        log_error "Essential package failure detected. Options:"
        log_error "1. Continue deployment (may cause issues later)"
        log_error "2. Retry this package manually"
        log_error "3. Skip this package (not recommended)"

        # In automated mode, we'll continue but log the critical failure
        log_warning "Continuing deployment despite essential package failure (automated mode)"
      else
        log_success "✅ Essential package $package installed successfully"
      fi

      # Show progress
      local completed_count=$(wc -l < "${PROGRESS_FILE}.completed" 2>/dev/null || echo "0")
      log_info "Progress: $completed_count packages completed so far"
      echo ""
    done
  fi

  # ENHANCED: Process only optional packages that actually need installation
  log_info "🔧 Phase 2: Installing optional packages..."

  # Filter optional packages to only those that need installation
  local optional_to_install=()
  for package in "${optional_packages[@]}"; do
    if [[ " ${MISSING_PACKAGES[*]} " =~ " ${package} " ]]; then
      optional_to_install+=("$package")
    fi
  done

  log_info "📦 Optional packages needing installation: ${#optional_to_install[@]}/${#optional_packages[@]}"

  if [[ ${#optional_to_install[@]} -eq 0 ]]; then
    log_success "✅ All optional packages already installed - skipping individual installation"
  else
    for package in "${optional_to_install[@]}"; do
      current_package=$((current_package + 1))

      log_info "--- Processing optional package $package ($current_package/$total_packages) ---"

      if ! install_single_package "$package" "$current_package" "$total_packages" "false"; then
        failed_optional+=("$package")
        log_warning "⚠️  Optional package $package failed - continuing deployment"
      else
        log_success "✅ Optional package $package installed successfully"
      fi

      # Show progress
      local completed_count=$(wc -l < "${PROGRESS_FILE}.completed" 2>/dev/null || echo "0")
      log_info "Progress: $completed_count packages completed so far"
      echo ""
    done
  fi

  local duration=$(($(date +%s) - start_time))

  # Generate comprehensive report
  log_info "=== INSTALLATION SUMMARY ==="
  log_info "Total installation time: ${duration}s ($(($duration / 60))m $(($duration % 60))s)"

  local completed_count=$(wc -l < "${PROGRESS_FILE}.completed" 2>/dev/null || echo "0")

  log_info "Successfully installed: $completed_count packages"
  log_info "Failed essential packages: ${#failed_essential[@]}"
  log_info "Failed optional packages: ${#failed_optional[@]}"

  if [[ ${#failed_essential[@]} -gt 0 ]]; then
    log_warning "Failed essential packages: ${failed_essential[*]}"
  fi

  if [[ ${#failed_optional[@]} -gt 0 ]]; then
    log_info "Failed optional packages: ${failed_optional[*]}"
  fi

  # Decision logic for continuation
  if [[ ${#failed_essential[@]} -eq 0 ]]; then
    if [[ ${#failed_optional[@]} -eq 0 ]]; then
      log_success "🎉 All system packages installed successfully!"
    else
      log_success "✅ Essential packages installed successfully, ${#failed_optional[@]} optional packages failed (acceptable)"
    fi

    # Update phase completion
    echo "nodejs" > "${PROGRESS_FILE}.phase" 2>/dev/null || true

    return 0
  else
    log_error "❌ CRITICAL: ${#failed_essential[@]} essential packages failed"
    log_error "This may cause deployment instability"
    log_warning "You can:"
    log_warning "1. Re-run this script to retry failed packages"
    log_warning "2. Manually install failed packages: ${failed_essential[*]}"
    log_warning "3. Continue deployment (not recommended)"

    return 1
  fi
}

# =============================================================================
# SIMPLE NODE.JS INSTALLATION
# =============================================================================

install_nodejs() {
  log_info "Installing Node.js 20+ (required for application dependencies)..."

  # Check if Node.js 20+ is already installed
  if command_exists node; then
    local node_version=$(node --version)
    local major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
    if [[ "$major_version" -ge 20 ]]; then
      log_success "Node.js 20+ already installed: $node_version"
      return 0
    else
      log_warning "Found Node.js $node_version, but need version 20+. Upgrading..."
      # Remove old version
      apt-get remove -y nodejs npm 2>/dev/null || true
    fi
  fi

  export DEBIAN_FRONTEND=noninteractive

  # Fix DNS resolution issues first
  log_info "Fixing DNS resolution..."
  echo "nameserver 8.8.8.8" >> /etc/resolv.conf
  echo "nameserver 1.1.1.1" >> /etc/resolv.conf

  # Method 1: Try NodeSource repository first (ensures Node.js 20+)
  log_info "Method 1: NodeSource repository for Node.js 20..."
  if curl -fsSL https://deb.nodesource.com/setup_20.x -o /tmp/nodesource_setup.sh >>"$LOG_FILE" 2>&1; then
    if timeout 180 bash /tmp/nodesource_setup.sh >>"$LOG_FILE" 2>&1; then
      if timeout 180 apt-get install -y nodejs >>"$LOG_FILE" 2>&1; then
        local node_version=$(node --version 2>/dev/null || echo "unknown")
        local major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
        if [[ "$major_version" -ge 20 ]]; then
          log_success "✅ Node.js 20+ installed via NodeSource: $node_version"
          log_info "npm version: $(npm --version)"
          rm -f /tmp/nodesource_setup.sh
          return 0
        else
          log_warning "⚠️ NodeSource installed $node_version, but need 20+"
        fi
      fi
    fi
    rm -f /tmp/nodesource_setup.sh
  fi

  # Method 2: Try Ubuntu repository (fallback, but validate version)
  log_info "Method 2: Ubuntu repository fallback..."
  if apt-get update --fix-missing; then
    if apt-get install -y nodejs npm; then
      local node_version=$(node --version 2>/dev/null || echo "unknown")
      local major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
      if [[ "$major_version" -ge 20 ]]; then
        log_success "✅ Node.js 20+ installed via Ubuntu repository: $node_version"
        return 0
      else
        log_warning "⚠️ Ubuntu repository has Node.js $node_version, but need 20+"
        # Remove the old version
        apt-get remove -y nodejs npm 2>/dev/null || true
      fi
    fi
  fi

  # Method 3: Try snap package (fallback with version validation)
  log_info "Method 3: Snap package fallback..."
  if command_exists snap; then
    if timeout 180 snap install node --classic >>"$LOG_FILE" 2>&1; then
      # Create symlinks for compatibility
      sudo ln -sf /snap/bin/node /usr/local/bin/node 2>/dev/null || true
      sudo ln -sf /snap/bin/npm /usr/local/bin/npm 2>/dev/null || true

      local node_version=$(node --version 2>/dev/null || echo "unknown")
      local major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
      if [[ "$major_version" -ge 20 ]]; then
        log_success "✅ Node.js 20+ installed via Snap: $node_version"
        return 0
      else
        log_warning "⚠️ Snap installed Node.js $node_version, but need 20+"
        snap remove node 2>/dev/null || true
      fi
    fi
  fi

  # Method 4: Direct binary download for Node.js 20 LTS (last resort)
  log_info "Method 4: Direct binary download for Node.js 20 LTS..."
  local node_version="v20.18.0"  # Latest Node.js 20 LTS
  local node_arch="linux-x64"
  local node_url="https://nodejs.org/dist/${node_version}/node-${node_version}-${node_arch}.tar.xz"

  if curl -fsSL "$node_url" -o "/tmp/node-${node_version}-${node_arch}.tar.xz" >>"$LOG_FILE" 2>&1; then
    if tar -xf "/tmp/node-${node_version}-${node_arch}.tar.xz" -C /tmp >>"$LOG_FILE" 2>&1; then
      sudo cp -r "/tmp/node-${node_version}-${node_arch}"/* /usr/local/ >>"$LOG_FILE" 2>&1
      rm -rf "/tmp/node-${node_version}-${node_arch}" "/tmp/node-${node_version}-${node_arch}.tar.xz"
      if command_exists node; then
        local installed_version=$(node --version)
        local major_version=$(echo "$installed_version" | sed 's/v\([0-9]*\).*/\1/')
        if [[ "$major_version" -ge 20 ]]; then
          log_success "✅ Node.js 20+ installed via direct binary: $installed_version"
          log_info "npm version: $(npm --version)"
          return 0
        else
          log_error "❌ Binary download installed $installed_version, but need 20+"
        fi
      fi
    fi
  fi

  log_error "❌ CRITICAL: All Node.js installation methods failed"
  log_error "❌ This is a deployment-blocking issue"
  log_error "❌ Manual intervention required"
  return 1
}

# =============================================================================
# SIMPLE PM2 INSTALLATION
# =============================================================================

install_pm2_global() {
  log_info "Installing PM2 process manager (simple approach)..."

  # Check if PM2 is already installed
  if command_exists pm2; then
    log_success "PM2 already installed: v$(pm2 --version)"
    return 0
  fi

  # Verify npm is available
  if ! command_exists npm; then
    log_error "npm not found, cannot install PM2"
    return 1
  fi

  # Simple PM2 installation
  log_info "Installing PM2 globally via npm..."
  if npm install -g pm2 >>"$LOG_FILE" 2>&1; then
    log_success "PM2 installed successfully"
    if command_exists pm2; then
      log_info "PM2 version: $(pm2 --version)"
      return 0
    fi
  fi

  log_error "PM2 installation failed"
  return 1
}

# =============================================================================
# SIMPLE SYSTEM CONFIGURATION
# =============================================================================

configure_basic_system() {
  log_info "Configuring basic system settings..."

  # Enable and start nginx
  if command_exists nginx; then
    systemctl enable nginx >>"$LOG_FILE" 2>&1 || true
    log_info "Nginx enabled for startup"
  fi

  # Set timezone to Asia/Manila if not set
  if [[ "$(timedatectl show -p Timezone --value)" != "Asia/Manila" ]]; then
    timedatectl set-timezone Asia/Manila >>"$LOG_FILE" 2>&1 || true
    log_info "Timezone set to Asia/Manila"
  fi

  log_success "Basic system configuration completed"
  return 0
}

# =============================================================================
# INTELLIGENT INSTALLATION FUNCTIONS
# =============================================================================

# Intelligent system packages installation
install_system_packages_intelligent() {
  log_info "🧠 Analyzing system packages with intelligent detection..."

  # Define required packages with minimum versions
  local -A required_packages=(
    ["curl"]=""
    ["wget"]=""
    ["git"]="2.25"
    ["build-essential"]=""
    ["software-properties-common"]=""
    ["apt-transport-https"]=""
    ["ca-certificates"]=""
    ["gnupg"]=""
    ["lsb-release"]=""
    ["unzip"]=""
    ["vim"]=""
    ["htop"]=""
    ["tree"]=""
    ["jq"]=""
  )

  local -a missing_packages=()
  local -a outdated_packages=()

  # Analyze each package
  for package in "${!required_packages[@]}"; do
    local min_version="${required_packages[$package]}"

    if ! package_exists "$package" "$min_version"; then
      if dpkg -s "$package" >/dev/null 2>&1; then
        outdated_packages+=("$package")
        log_info "   🔄 Outdated: $package (needs upgrade)"
      else
        missing_packages+=("$package")
        log_info "   ❌ Missing: $package"
      fi
    else
      log_info "   ✅ Current: $package"
    fi
  done

  # Calculate installation strategy
  local total_needed=$((${#missing_packages[@]} + ${#outdated_packages[@]}))

  if [[ $total_needed -eq 0 ]]; then
    log_success "✅ All system packages are current - SKIPPING installation"
    create_phase_completion_marker "system-packages" "all-current"
    return 0
  fi

  log_info "📊 Installation Summary:"
  log_info "   • Missing packages: ${#missing_packages[@]}"
  log_info "   • Outdated packages: ${#outdated_packages[@]}"
  log_info "   • Total to install/upgrade: $total_needed"

  # Combine packages for installation
  local -a all_packages=("${missing_packages[@]}" "${outdated_packages[@]}")

  # Use selective installation
  if install_missing_packages "${all_packages[@]}"; then
    log_success "✅ System packages installation completed successfully"
    create_phase_completion_marker "system-packages" "selective-install-$(date +%s)"
    return 0
  else
    log_error "❌ System packages installation failed"
    return 1
  fi
}

# Intelligent Node.js installation
install_nodejs_intelligent() {
  log_info "🟢 Analyzing Node.js installation with intelligent detection..."

  # Check if Node.js 20+ is already installed and functional
  if command_exists node && command_exists npm; then
    local node_version=$(node --version 2>/dev/null || echo "v0.0.0")
    local npm_version=$(npm --version 2>/dev/null || echo "0.0.0")
    local major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')

    if [[ "$major_version" -ge 20 ]]; then
      # Test if Node.js is functional
      if node -e "console.log('Node.js test successful')" >/dev/null 2>&1; then
        log_success "✅ Node.js $node_version is current and functional - SKIPPING installation"
        log_info "   • Node.js version: $node_version"
        log_info "   • NPM version: $npm_version"
        create_phase_completion_marker "nodejs" "functional-v$major_version"
        return 0
      else
        log_warning "⚠️ Node.js $node_version exists but is not functional - REINSTALLING"
      fi
    else
      log_warning "⚠️ Found Node.js $node_version, but need version 20+ - UPGRADING"
    fi
  else
    log_info "🔄 Node.js not found - INSTALLING"
  fi

  # Proceed with installation using existing function
  if install_nodejs; then
    create_phase_completion_marker "nodejs" "fresh-install-$(date +%s)"
    return 0
  else
    return 1
  fi
}

# =============================================================================
# MAIN INSTALLATION FUNCTION
# =============================================================================

main() {
  local start_time=$(date +%s)
  start_performance_timer "system-dependencies-total"

  log_info "🚀 Starting Intelligent System Dependencies Installation"
  log_info "📅 Started at: $(date)"
  log_info "🖥️ Environment: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Ubuntu')"
  log_info "🧠 Mode: Intelligent Detection with Selective Installation"

  # INTELLIGENT PRE-INSTALLATION ANALYSIS
  log_info "🔍 Phase 0/4: Pre-Installation Analysis"
  start_performance_timer "pre-installation-analysis"

  # Check if we can skip this entire phase
  if should_skip_installation "system-dependencies" "system_packages" "/usr/bin" 24; then
    log_success "✅ System dependencies are recent and valid - SKIPPING entire phase"
    end_performance_timer "pre-installation-analysis"
    end_performance_timer "system-dependencies-total"
    return 0
  fi

  end_performance_timer "pre-installation-analysis"

  # Phase 1: System packages with intelligent detection
  log_info "📦 Phase 1/4: System Packages Installation (Intelligent)"
  start_performance_timer "system-packages"

  if ! install_system_packages_intelligent; then
    log_error "❌ System packages installation failed"
    exit 1
  fi

  end_performance_timer "system-packages"

  # Phase 2: Node.js with version validation (CRITICAL - must succeed)
  log_info "🟢 Phase 2/4: Node.js Installation (CRITICAL - Intelligent)"
  start_performance_timer "nodejs-installation"

  if ! install_nodejs_intelligent; then
    log_error "❌ CRITICAL: Node.js installation failed"
    log_error "❌ Deployment cannot continue without Node.js"
    log_error "❌ This will cause Phase 3 (build-application) to fail"
    exit 1
  fi

  end_performance_timer "nodejs-installation"

  # Phase 3: PM2 with intelligent detection (continue on failure)
  log_info "🔄 Phase 3/4: PM2 Installation (Intelligent - Optional)"
  start_performance_timer "pm2-installation"

  if command_exists pm2; then
    log_success "✅ PM2 already installed - SKIPPING"
  else
    if ! install_pm2_global; then
      log_warning "⚠️ PM2 installation failed - will be installed later in deployment"
    fi
  fi

  end_performance_timer "pm2-installation"

  # Phase 4: Basic configuration with intelligent detection (always continue)
  log_info "⚙️ Phase 4/4: Basic System Configuration (Intelligent)"
  start_performance_timer "basic-configuration"

  if ! configure_basic_system; then
    log_warning "⚠️ Basic system configuration had issues, but continuing..."
  fi

  end_performance_timer "basic-configuration"

  # Final validation with intelligent comprehensive testing
  log_info "✅ Final Validation: Comprehensive System Health Check"
  start_performance_timer "final-validation"

  local critical_missing=0
  local warnings=0

  # Essential tools validation (must be present and functional)
  log_info "🔍 Validating essential tools..."
  for cmd in git curl; do
    if command_exists "$cmd"; then
      # Test functionality
      case "$cmd" in
        "git")
          if git --version >/dev/null 2>&1; then
            log_success "   ✅ $cmd is available and functional"
          else
            log_error "   ❌ CRITICAL: $cmd exists but is not functional"
            critical_missing=$((critical_missing + 1))
          fi
          ;;
        "curl")
          if curl --version >/dev/null 2>&1; then
            log_success "   ✅ $cmd is available and functional"
          else
            log_error "   ❌ CRITICAL: $cmd exists but is not functional"
            critical_missing=$((critical_missing + 1))
          fi
          ;;
      esac
    else
      log_error "   ❌ CRITICAL: $cmd not found"
      critical_missing=$((critical_missing + 1))
    fi
  done

  # Optional tools validation with version checking
  log_info "🔍 Validating optional tools..."
  for cmd in node npm pm2 nginx; do
    if command_exists "$cmd"; then
      case "$cmd" in
        "node")
          local node_version=$(node --version 2>/dev/null || echo "unknown")
          local major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
          if [[ "$major_version" -ge 20 ]]; then
            log_success "   ✅ $cmd $node_version (meets requirements)"
          else
            log_warning "   ⚠️ $cmd $node_version (version 20+ recommended)"
            warnings=$((warnings + 1))
          fi
          ;;
        *)
          log_success "   ✅ $cmd is available"
          ;;
      esac
    else
      log_info "   ℹ️ $cmd not found (will be installed later)"
    fi
  done

  end_performance_timer "final-validation"
  end_performance_timer "system-dependencies-total"

  # Generate performance report
  generate_performance_report

  # Final completion processing
  if [[ $critical_missing -eq 0 ]]; then
    log_success "🎉 System Dependencies Installation completed successfully!"

    if [[ $warnings -gt 0 ]]; then
      log_info "⚠️ Completed with $warnings warnings (non-critical)"
    fi

    # Show versions of available tools
    log_info "📋 Installed Tools Summary:"
    command_exists node && log_info "   • Node.js: $(node --version)"
    command_exists npm && log_info "   • npm: $(npm --version)"
    command_exists pm2 && log_info "   • PM2: $(pm2 --version 2>/dev/null || echo 'not installed')"
    command_exists nginx && log_info "   • Nginx: $(nginx -v 2>&1 | head -1 || echo 'not installed')"
    command_exists git && log_info "   • Git: $(git --version | head -1)"
    command_exists curl && log_info "   • cURL: $(curl --version | head -1)"

    # Create intelligent completion marker
    log_info "📍 Creating intelligent completion markers..."
    create_phase_completion_marker "system-dependencies-complete" "intelligent-install-$(date +%s)"

    # Legacy completion marker for compatibility
    local completion_marker="${LOG_DIR}/phase-1-completed-$(date +%Y%m%d-%H%M%S)"
    cat > "$completion_marker" << EOF
PHASE_1_SYSTEM_DEPENDENCIES_COMPLETED=true
COMPLETION_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
CRITICAL_MISSING=$critical_missing
WARNINGS=$warnings
INSTALLATION_MODE=intelligent
EOF

    # Update progress tracking
    if [[ -f "${PROGRESS_FILE}.completed" ]]; then
      echo "PHASE_1_SYSTEM_DEPENDENCIES" >> "${PROGRESS_FILE}.completed"
    fi

    log_success "✅ Phase 1 intelligent completion - ready for Phase 2"
    sync
    return 0
  else
    log_error "Installation failed - $critical_missing critical tools missing"

    # CRITICAL FIX: Explicit failure signaling
    log_error "🚨 Signaling phase failure to deployment orchestrator..."

    # Create failure marker file
    local failure_marker="${LOG_DIR}/phase-1-failed-$(date +%Y%m%d-%H%M%S)"
    echo "PHASE_1_SYSTEM_DEPENDENCIES_FAILED" > "$failure_marker"
    echo "FAILURE_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> "$failure_marker"
    echo "CRITICAL_MISSING=$critical_missing" >> "$failure_marker"
    echo "DURATION_SECONDS=$total_duration" >> "$failure_marker"

    # Update progress tracking with explicit failure
    if [[ -f "${PROGRESS_FILE}.failed" ]]; then
      echo "PHASE_1_SYSTEM_DEPENDENCIES" >> "${PROGRESS_FILE}.failed"
    fi

    # Signal failure to parent process
    log_error "❌ Phase 1 failure signal sent"

    # Ensure all output is flushed
    sync

    return 1
  fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
